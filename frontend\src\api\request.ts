import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig, AxiosError } from 'axios'
import { ElMessage, ElLoading } from 'element-plus'
import { useAuthStore } from '@/store/auth'
import router from '@/router'
import { cache } from '@/utils/cache'
import { errorHandler } from '@/utils/error-handler'

// 创建axios实例
const request: AxiosInstance = axios.create({
  baseURL: '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求加载状态
let loadingInstance: any = null
let requestCount = 0

// 显示加载
const showLoading = () => {
  if (requestCount === 0) {
    loadingInstance = ElLoading.service({
      text: '加载中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })
  }
  requestCount++
}

// 隐藏加载
const hideLoading = () => {
  requestCount--
  if (requestCount <= 0) {
    requestCount = 0
    if (loadingInstance) {
      loadingInstance.close()
      loadingInstance = null
    }
  }
}

// 生成缓存键
function generateCacheKey(config: AxiosRequestConfig | InternalAxiosRequestConfig): string {
  const { method, url, params, data } = config
  return `${method}:${url}:${JSON.stringify(params)}:${JSON.stringify(data)}`
}

// 请求拦截器
request.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 检查缓存（仅对GET请求）
    if (config.method === 'get' && config.cache !== false) {
      const cacheKey = generateCacheKey(config)
      const cachedData = cache.get(cacheKey)
      if (cachedData) {
        // 返回缓存的数据
        return Promise.resolve({
          ...config,
          data: cachedData,
          cached: true
        } as any)
      }
    }

    // 显示加载状态
    if (config.loading !== false) {
      showLoading()
    }

    // 添加认证头
    const token = localStorage.getItem('access_token')
    if (token && config.headers) {
      // 检查token是否即将过期
      try {
        const tokenData = JSON.parse(atob(token.split('.')[1]))
        const expirationTime = tokenData.exp * 1000
        const currentTime = Date.now()
        const timeToExpire = expirationTime - currentTime
        
        // 如果token将在5分钟内过期，主动刷新
        if (timeToExpire > 0 && timeToExpire < 5 * 60 * 1000) {
          const authStore = useAuthStore()
          // 使用Promise.resolve()来处理异步刷新
          return Promise.resolve().then(async () => {
            await authStore.refreshAccessToken()
            const newToken = localStorage.getItem('access_token')
            if (newToken) {
              config.headers.Authorization = `Bearer ${newToken}`
            }
            return config
          })
        }
        config.headers.Authorization = `Bearer ${token}`
      } catch (error) {
        console.error('Token解析失败:', error)
        config.headers.Authorization = `Bearer ${token}`
      }
    }

    return config
  },
  (error) => {
    hideLoading()
    return Promise.reject(error)
  }
)

// Token刷新状态管理
let isRefreshing = false
let failedQueue: Array<{ resolve: Function; reject: Function }> = []

// 处理队列中的请求
const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error)
    } else {
      resolve(token)
    }
  })
  
  failedQueue = []
}

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse) => {
    hideLoading()

    // 缓存GET请求的响应（如果启用缓存）
    if (response.config.method === 'get' && response.config.cache !== false) {
      const cacheKey = generateCacheKey(response.config)
      const cacheTTL = response.config.cacheTTL || 5 * 60 * 1000 // 默认5分钟
      cache.set(cacheKey, response.data, cacheTTL)
    }

    return response.data
  },
  async (error) => {
    hideLoading()

    const { response } = error
    const originalRequest = error.config

    // 特殊处理401错误的令牌刷新逻辑
    if (response?.status === 401 && !originalRequest._retry) {
      const refreshToken = localStorage.getItem('refresh_token')
      
      if (!refreshToken) {
        // 没有刷新令牌，直接处理错误
        errorHandler.handleAPIError(error)
        return Promise.reject(error)
      }

      if (isRefreshing) {
        // 如果正在刷新，将请求加入队列
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject })
        }).then(token => {
          originalRequest.headers.Authorization = `Bearer ${token}`
          return request(originalRequest)
        }).catch(err => {
          return Promise.reject(err)
        })
      }

      originalRequest._retry = true
      isRefreshing = true

      try {
        const authStore = useAuthStore()
        await authStore.refreshAccessToken()
        const newToken = localStorage.getItem('access_token') || localStorage.getItem('token')
        
        if (newToken) {
          // 处理队列中的请求
          processQueue(null, newToken)
          
          // 重新发送原请求
          originalRequest.headers.Authorization = `Bearer ${newToken}`
          return request(originalRequest)
        } else {
          throw new Error('刷新令牌后未获取到新的访问令牌')
        }
      } catch (error) {
        // 刷新失败，处理队列中的请求
        processQueue(error, null)
        
        // 只有在刷新失败时才处理错误，避免重复错误处理
        const refreshError = error as AxiosError
        if (refreshError.response?.status !== 401) {
          errorHandler.handleAPIError(refreshError)
        } else {
          // 401错误表示认证失败，直接清除认证信息并跳转
          localStorage.removeItem('access_token')
          localStorage.removeItem('refresh_token')
          setTimeout(() => {
            window.location.href = '/login'
          }, 100)
        }
        return Promise.reject(refreshError)
      } finally {
        isRefreshing = false
      }
    }

    // 其他错误使用统一错误处理器
    errorHandler.handleAPIError(error)
    return Promise.reject(error)
  }
)

// 扩展AxiosRequestConfig类型
declare module 'axios' {
  interface AxiosRequestConfig {
    loading?: boolean
    _retry?: boolean
    cache?: boolean
    cacheTTL?: number
    cached?: boolean
  }
  
  interface InternalAxiosRequestConfig {
    loading?: boolean
    _retry?: boolean
    cache?: boolean
    cacheTTL?: number
    cached?: boolean
  }
}

export default request
