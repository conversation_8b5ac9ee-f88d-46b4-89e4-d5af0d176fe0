/**
 * 统一错误处理工具
 */
import { ElMessage, ElNotification } from 'element-plus'
import type { AxiosError } from 'axios'

// 错误类型定义
export interface APIError {
  code: string
  message: string
  details?: any
  timestamp: string
  request_id?: string
}

export interface ErrorResponse {
  success: false
  error: APIError
}

// 错误级别
export enum ErrorLevel {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical'
}

// 错误分类
export enum ErrorCategory {
  NETWORK = 'network',
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  VALIDATION = 'validation',
  BUSINESS = 'business',
  SYSTEM = 'system'
}

class ErrorHandler {
  private errorLog: Array<{
    error: APIError
    level: ErrorLevel
    category: ErrorCategory
    timestamp: Date
    url?: string
    method?: string
  }> = []

  /**
   * 处理API错误
   */
  handleAPIError(error: AxiosError): void {
    const response = error.response
    const request = error.request
    const config = error.config

    if (response) {
      // 服务器返回错误响应
      const errorData = response.data as any
      
      // 检查是否是后端标准错误格式: {error: true, message: '...', status_code: 401}
      if (errorData?.error === true && errorData?.message) {
        // 转换为前端期望的格式
        const apiError: APIError = {
          code: this.getErrorCodeFromStatus(response.status),
          message: errorData.message,
          details: errorData.details,
          timestamp: new Date().toISOString(),
          request_id: errorData.request_id
        }
        this.processAPIError(apiError, config?.url, config?.method)
      } else if (errorData?.error) {
        // 前端标准错误格式: {success: false, error: {code: '...', message: '...'}}
        this.processAPIError(errorData.error, config?.url, config?.method)
      } else {
        // 非标准错误响应
        this.showError(`请求失败 (${response.status})`, response.statusText || errorData?.message || '未知错误')
      }
    } else if (request) {
      // 网络错误
      this.handleNetworkError()
    } else {
      // 请求配置错误
      this.showError('请求配置错误', error.message)
    }
  }

  /**
   * 根据HTTP状态码获取错误代码
   */
  private getErrorCodeFromStatus(status: number): string {
    switch (status) {
      case 400:
        return 'BAD_REQUEST'
      case 401:
        return 'AUTHENTICATION_ERROR'
      case 403:
        return 'AUTHORIZATION_ERROR'
      case 404:
        return 'NOT_FOUND'
      case 422:
        return 'VALIDATION_ERROR'
      case 500:
        return 'INTERNAL_SERVER_ERROR'
      default:
        return 'UNKNOWN_ERROR'
    }
  }

  /**
   * 处理标准API错误
   */
  private processAPIError(apiError: APIError, url?: string, method?: string): void {
    // 确保 apiError.code 存在，如果不存在则提供默认值
    const errorCode = apiError?.code || 'UNKNOWN_ERROR'
    const category = this.categorizeError(errorCode)
    const level = this.determineErrorLevel(errorCode)

    // 记录错误
    this.logError(apiError, level, category, url, method)

    // 显示错误信息
    this.displayError(apiError, level, category)
  }

  /**
   * 错误分类
   */
  private categorizeError(errorCode: string): ErrorCategory {
    // 添加空值检查
    if (!errorCode || typeof errorCode !== 'string') {
      return ErrorCategory.BUSINESS
    }
    
    if (errorCode.includes('AUTHENTICATION')) {
      return ErrorCategory.AUTHENTICATION
    }
    if (errorCode.includes('AUTHORIZATION')) {
      return ErrorCategory.AUTHORIZATION
    }
    if (errorCode.includes('VALIDATION')) {
      return ErrorCategory.VALIDATION
    }
    if (errorCode.includes('NETWORK') || errorCode.includes('TIMEOUT')) {
      return ErrorCategory.NETWORK
    }
    if (errorCode.includes('INTERNAL_SERVER')) {
      return ErrorCategory.SYSTEM
    }
    return ErrorCategory.BUSINESS
  }

  /**
   * 确定错误级别
   */
  private determineErrorLevel(errorCode: string): ErrorLevel {
    // 添加空值检查
    if (!errorCode || typeof errorCode !== 'string') {
      return ErrorLevel.ERROR
    }
    
    if (errorCode.includes('CRITICAL') || errorCode.includes('INTERNAL_SERVER')) {
      return ErrorLevel.CRITICAL
    }
    if (errorCode.includes('AUTHENTICATION') || errorCode.includes('AUTHORIZATION')) {
      return ErrorLevel.ERROR
    }
    if (errorCode.includes('VALIDATION') || errorCode.includes('NOT_FOUND')) {
      return ErrorLevel.WARNING
    }
    return ErrorLevel.INFO
  }

  /**
   * 显示错误信息
   */
  private displayError(apiError: APIError, level: ErrorLevel, category: ErrorCategory): void {
    const message = this.formatErrorMessage(apiError)

    switch (level) {
      case ErrorLevel.CRITICAL:
        ElNotification({
          title: '系统错误',
          message,
          type: 'error',
          duration: 0, // 不自动关闭
          showClose: true
        })
        break

      case ErrorLevel.ERROR:
        ElMessage.error(message)
        break

      case ErrorLevel.WARNING:
        ElMessage.warning(message)
        break

      case ErrorLevel.INFO:
        ElMessage.info(message)
        break
    }

    // 特殊处理认证错误
    if (category === ErrorCategory.AUTHENTICATION) {
      this.handleAuthenticationError()
    }
  }

  /**
   * 格式化错误消息
   */
  private formatErrorMessage(apiError: APIError): string {
    let message = apiError.message

    // 添加详细信息
    if (apiError.details) {
      if (typeof apiError.details === 'string') {
        message += `: ${apiError.details}`
      } else if (apiError.details.summary) {
        message += `: ${apiError.details.summary}`
      }
    }

    // 添加请求ID（用于调试）
    if (apiError.request_id && import.meta.env.DEV) {
      message += ` (ID: ${apiError.request_id.slice(0, 8)})`
    }

    return message
  }

  /**
   * 处理网络错误
   */
  private handleNetworkError(): void {
    ElMessage.error('网络连接失败，请检查网络设置')
    
    this.logError(
      {
        code: 'NETWORK_ERROR',
        message: '网络连接失败',
        timestamp: new Date().toISOString()
      },
      ErrorLevel.ERROR,
      ErrorCategory.NETWORK
    )
  }

  /**
   * 处理认证错误
   */
  private handleAuthenticationError(): void {
    // 清除本地认证信息
    localStorage.removeItem('access_token')
    localStorage.removeItem('refresh_token')
    
    // 延迟跳转到登录页，避免与其他路由操作冲突
    setTimeout(() => {
      window.location.href = '/login'
    }, 1000)
  }

  /**
   * 显示通用错误
   */
  private showError(title: string, message: string): void {
    ElMessage.error(`${title}: ${message}`)
  }

  /**
   * 记录错误
   */
  private logError(
    error: APIError,
    level: ErrorLevel,
    category: ErrorCategory,
    url?: string,
    method?: string
  ): void {
    const logEntry = {
      error,
      level,
      category,
      timestamp: new Date(),
      url,
      method
    }

    this.errorLog.push(logEntry)

    // 限制日志大小
    if (this.errorLog.length > 100) {
      this.errorLog.shift()
    }

    // 开发环境下打印到控制台（只记录错误和关键级别）
    if (import.meta.env.DEV && (level === ErrorLevel.ERROR || level === ErrorLevel.CRITICAL)) {
      console.error('API Error:', logEntry)
    }

    // 发送错误到监控服务（如果需要）
    this.reportError(logEntry)
  }

  /**
   * 上报错误到监控服务
   */
  private reportError(logEntry: any): void {
    // 这里可以集成错误监控服务，如 Sentry
    // 暂时只在控制台记录
    if (logEntry.level === ErrorLevel.CRITICAL) {
      console.error('Critical error reported:', logEntry)
    }
  }

  /**
   * 获取错误日志
   */
  getErrorLog(): typeof this.errorLog {
    return [...this.errorLog]
  }

  /**
   * 清空错误日志
   */
  clearErrorLog(): void {
    this.errorLog = []
  }

  /**
   * 获取错误统计
   */
  getErrorStats(): {
    total: number
    byLevel: Record<ErrorLevel, number>
    byCategory: Record<ErrorCategory, number>
  } {
    const stats = {
      total: this.errorLog.length,
      byLevel: {} as Record<ErrorLevel, number>,
      byCategory: {} as Record<ErrorCategory, number>
    }

    // 初始化计数器
    Object.values(ErrorLevel).forEach(level => {
      stats.byLevel[level] = 0
    })
    Object.values(ErrorCategory).forEach(category => {
      stats.byCategory[category] = 0
    })

    // 统计
    this.errorLog.forEach(entry => {
      stats.byLevel[entry.level]++
      stats.byCategory[entry.category]++
    })

    return stats
  }
}

// 创建全局错误处理器实例
export const errorHandler = new ErrorHandler()

// 导出便捷方法
export const handleError = (error: AxiosError) => errorHandler.handleAPIError(error)

export default errorHandler
