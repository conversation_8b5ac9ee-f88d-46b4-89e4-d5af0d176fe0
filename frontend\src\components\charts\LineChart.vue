<template>
  <BaseChart
    :width="width"
    :height="height"
    :options="chartOptions"
    :loading="loading"
    :error="error"
    :theme="theme"
    @chart-ready="handleChartReady"
    @chart-click="handleChartClick"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import BaseChart from './BaseChart.vue'
import type { EChartsOption } from 'echarts'

// Props定义
interface DataItem {
  name: string
  value: number
  [key: string]: any
}

interface SeriesData {
  name: string
  data: (number | DataItem)[]
  color?: string
  type?: 'line' | 'bar'
  smooth?: boolean
  area?: boolean
}

interface Props {
  width?: string
  height?: string
  data: SeriesData[]
  xAxisData?: string[]
  title?: string
  subtitle?: string
  loading?: boolean
  error?: string
  theme?: string
  showLegend?: boolean
  showGrid?: boolean
  showTooltip?: boolean
  showDataZoom?: boolean
  smooth?: boolean
  area?: boolean
  colors?: string[]
}

const props = withDefaults(defineProps<Props>(), {
  width: '100%',
  height: '400px',
  loading: false,
  error: '',
  theme: 'default',
  showLegend: true,
  showGrid: true,
  showTooltip: true,
  showDataZoom: false,
  smooth: false,
  area: false,
  colors: () => ['#409eff', '#67c23a', '#e6a23c', '#f56c6c', '#909399']
})

// Emits定义
const emit = defineEmits<{
  chartReady: [chart: any]
  chartClick: [params: any]
}>()

// 计算图表配置
const chartOptions = computed<EChartsOption>(() => {
  const option: EChartsOption = {
    title: props.title ? {
      text: props.title,
      subtext: props.subtitle,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#303133'
      },
      subtextStyle: {
        fontSize: 12,
        color: '#909399'
      }
    } : undefined,

    tooltip: props.showTooltip ? {
      trigger: 'axis',
      backgroundColor: 'rgba(50, 50, 50, 0.9)',
      borderColor: 'transparent',
      textStyle: {
        color: '#fff',
        fontSize: 12
      },
      formatter: (params: any) => {
        if (!Array.isArray(params)) params = [params]
        
        let result = `<div style="margin-bottom: 4px;">${params[0].axisValue}</div>`
        params.forEach((param: any) => {
          const color = param.color
          const seriesName = param.seriesName
          const value = param.value
          result += `
            <div style="display: flex; align-items: center; margin-bottom: 2px;">
              <span style="display: inline-block; width: 10px; height: 10px; background-color: ${color}; border-radius: 50%; margin-right: 6px;"></span>
              <span style="margin-right: 8px;">${seriesName}:</span>
              <span style="font-weight: bold;">${value}</span>
            </div>
          `
        })
        return result
      }
    } : undefined,

    legend: props.showLegend ? {
      top: props.title ? 40 : 10,
      left: 'center',
      textStyle: {
        color: '#606266',
        fontSize: 12
      }
    } : undefined,

    grid: props.showGrid ? {
      left: '3%',
      right: '4%',
      bottom: props.showDataZoom ? '15%' : '3%',
      top: props.showLegend ? (props.title ? 80 : 50) : (props.title ? 60 : 20),
      containLabel: true
    } : undefined,

    xAxis: {
      type: 'category',
      data: props.xAxisData || [],
      axisLine: {
        lineStyle: {
          color: '#e4e7ed'
        }
      },
      axisLabel: {
        color: '#606266',
        fontSize: 12
      },
      axisTick: {
        show: false
      }
    },

    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisLabel: {
        color: '#606266',
        fontSize: 12
      },
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: '#f0f2f5',
          type: 'dashed'
        }
      }
    },

    dataZoom: props.showDataZoom ? [
      {
        type: 'slider',
        show: true,
        xAxisIndex: [0],
        start: 0,
        end: 100,
        height: 20,
        bottom: 10,
        handleStyle: {
          color: '#409eff'
        },
        textStyle: {
          color: '#606266'
        }
      }
    ] : undefined,

    color: props.colors,

    series: Array.isArray(props.data) ? props.data.map((series, index) => ({
      name: series.name,
      type: series.type || 'line',
      data: series.data,
      smooth: series.smooth !== undefined ? series.smooth : props.smooth,
      areaStyle: (series.area !== undefined ? series.area : props.area) ? {
        opacity: 0.3
      } : undefined,
      lineStyle: {
        width: 2
      },
      symbol: 'circle',
      symbolSize: 6,
      itemStyle: {
        color: series.color || props.colors[index % props.colors.length]
      },
      emphasis: {
        focus: 'series',
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.3)'
        }
      }
    })) : []
  }

  return option
})

// 事件处理
const handleChartReady = (chart: any) => {
  emit('chartReady', chart)
}

const handleChartClick = (params: any) => {
  emit('chartClick', params)
}
</script>
