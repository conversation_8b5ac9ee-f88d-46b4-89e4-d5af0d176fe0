<template>
  <div class="performance-monitoring">
    <!-- 性能控制面板 -->
    <el-card class="control-panel">
      <div class="panel-header">
        <h3>性能监控控制台</h3>
        <div class="panel-actions">
          <el-button type="primary" @click="refreshAll" :loading="store.loading">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
          <el-button @click="exportPerformanceReport" :loading="exporting">
            <el-icon><Download /></el-icon>
            导出报告
          </el-button>
          <el-button @click="showAdvancedConfig = true">
            <el-icon><Setting /></el-icon>
            高级配置
          </el-button>
          <el-switch
            v-model="store.realTimeEnabled"
            @change="store.toggleRealTimeMonitoring"
            active-text="实时监控"
            inactive-text="暂停监控"
            style="margin-left: 16px;"
          />
        </div>
      </div>

      <!-- 系统健康状态概览 -->
      <div class="health-overview" v-if="store.systemHealth">
        <el-row :gutter="16">
          <el-col :span="6">
            <div class="health-summary">
              <el-tag :type="getHealthStatusType()" size="large">
                <el-icon><component :is="getHealthStatusIcon()" /></el-icon>
                系统状态: {{ store.systemHealth.overall === 'healthy' ? '良好' : store.systemHealth.overall === 'warning' ? '警告' : '异常' }}
              </el-tag>
              <div class="uptime">运行时间: {{ formatUptime(store.systemHealth.uptime) }}</div>
            </div>
          </el-col>
          <el-col :span="18">
            <div class="health-components">
              <div
                v-for="component in store.systemHealth.components"
                :key="component.name"
                :class="['health-component', component.status]"
              >
                <div class="component-icon" :style="{ backgroundColor: component.color }">
                  <el-icon><component :is="component.icon" /></el-icon>
                </div>
                <div class="component-info">
                  <div class="component-name">{{ component.name }}</div>
                  <div class="component-status">{{ component.statusText }}</div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 监控配置 -->
      <div class="monitoring-config">
        <el-form :model="config" inline>
          <el-form-item label="刷新间隔">
            <el-select v-model="config.refreshInterval" @change="updateRefreshInterval">
              <el-option label="5秒" :value="5" />
              <el-option label="10秒" :value="10" />
              <el-option label="30秒" :value="30" />
              <el-option label="1分钟" :value="60" />
            </el-select>
          </el-form-item>

          <el-form-item label="数据保留">
            <el-select v-model="config.dataRetention">
              <el-option label="最近1小时" value="1h" />
              <el-option label="最近6小时" value="6h" />
              <el-option label="最近24小时" value="24h" />
              <el-option label="最近7天" value="7d" />
            </el-select>
          </el-form-item>

          <el-form-item label="告警阈值">
            <el-input-number
              v-model="config.alertThreshold"
              :min="50"
              :max="95"
              :step="5"
              controls-position="right"
            />
            <span style="margin-left: 8px;">%</span>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" size="small" @click="showAdvancedConfig = true">
              <el-icon><Setting /></el-icon>
              高级配置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 钻取分析提示 -->
    <el-alert
      v-if="focusedMetric"
      :title="`正在查看 ${route.query.metric} 的详细分析`"
      type="info"
      :closable="true"
      @close="focusedMetric = null"
      style="margin-bottom: 24px;"
    >
      <template #default>
        <p>您已从概览页面钻取到此指标的详细视图。可以查看更多相关的性能数据和历史趋势。</p>
      </template>
    </el-alert>

    <!-- 快速统计和前端性能 -->
    <el-row :gutter="24" style="margin-top: 24px;">
      <!-- 快速统计 -->
      <el-col :xs="24" :lg="12">
        <el-card class="quick-stats-card">
          <template #header>
            <span>业务统计</span>
          </template>
          <div class="quick-stats" v-if="store.quickStats">
            <div class="stat-item">
              <div class="stat-icon" style="background-color: #3b82f6;">
                <el-icon><User /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ formatNumber(store.quickStats.onlineUsers) }}</div>
                <div class="stat-label">在线用户</div>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon" style="background-color: #10b981;">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ formatNumber(store.quickStats.apiRequestsPerMinute) }}</div>
                <div class="stat-label">API请求/分钟</div>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon" style="background-color: #ef4444;">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ store.quickStats.errorRate.toFixed(2) }}%</div>
                <div class="stat-label">错误率</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 前端性能 -->
      <el-col :xs="24" :lg="12">
        <el-card class="frontend-performance-card">
          <template #header>
            <div class="card-header">
              <span>前端性能</span>
              <el-button size="small" @click="refreshFrontendMetrics">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          <div class="frontend-metrics" v-if="store.frontendMetrics">
            <div class="metric-row">
              <span class="metric-label">页面加载时间</span>
              <span class="metric-value">{{ store.frontendMetrics.pageLoad.loadComplete }}ms</span>
            </div>
            <div class="metric-row">
              <span class="metric-label">JS内存使用</span>
              <span class="metric-value">{{ store.frontendMetrics.memory.usedJSHeapSize }}MB</span>
            </div>
            <div class="metric-row">
              <span class="metric-label">缓存命中率</span>
              <span class="metric-value">{{ store.frontendMetrics.cache.hitRate }}%</span>
            </div>
            <div class="metric-row">
              <span class="metric-label">API响应时间</span>
              <span class="metric-value">{{ store.frontendMetrics.api.averageResponseTime }}ms</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 性能图表 -->
    <div class="performance-charts">
      <el-row :gutter="24">
        <!-- CPU使用率趋势 -->
        <el-col :xs="24" :lg="12">
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>CPU使用率趋势</span>
                <el-button size="small" @click="toggleCpuChart">
                  {{ showCpuDetails ? '简化视图' : '详细视图' }}
                </el-button>
              </div>
            </template>
            <div class="chart-container">
              <LineChart
                :data="cpuChartData"
                :options="cpuChartOptions"
                height="300px"
              />
            </div>
          </el-card>
        </el-col>

        <!-- 内存使用情况 -->
        <el-col :xs="24" :lg="12">
          <el-card class="chart-card">
            <template #header>
              <span>内存使用分布</span>
            </template>
            <div class="chart-container">
              <DoughnutChart
                :data="memoryChartData"
                :options="memoryChartOptions"
                height="300px"
              />
              <div class="memory-details">
                <div v-for="item in memoryDetails" :key="item.label" class="memory-item">
                  <div class="memory-color" :style="{ backgroundColor: item.color }"></div>
                  <div class="memory-info">
                    <div class="memory-label">{{ item.label }}</div>
                    <div class="memory-value">{{ item.value }}</div>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="24" style="margin-top: 24px;">
        <!-- 网络流量监控 -->
        <el-col :xs="24" :lg="16">
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>网络流量监控</span>
                <el-select v-model="networkTimeRange" size="small" @change="updateNetworkChart">
                  <el-option label="最近1小时" value="1h" />
                  <el-option label="最近6小时" value="6h" />
                  <el-option label="最近24小时" value="24h" />
                </el-select>
              </div>
            </template>
            <div class="chart-container">
              <AreaChart
                :data="networkChartData"
                :options="networkChartOptions"
                height="300px"
              />
            </div>
          </el-card>
        </el-col>

        <!-- 系统负载 -->
        <el-col :xs="24" :lg="8">
          <el-card class="chart-card">
            <template #header>
              <span>系统负载</span>
            </template>
            <div class="chart-container">
              <div class="load-gauges">
                <div v-for="load in systemLoads" :key="load.label" class="load-gauge">
                  <div class="gauge-container">
                    <el-progress
                      type="circle"
                      :percentage="load.value"
                      :color="getLoadColor(load.value)"
                      :width="80"
                      :stroke-width="8"
                    >
                      <template #default="{ percentage }">
                        <span class="gauge-text">{{ percentage }}%</span>
                      </template>
                    </el-progress>
                  </div>
                  <div class="gauge-label">{{ load.label }}</div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 性能警告 -->
    <el-card v-if="performanceAlerts.length > 0" class="alerts-card">
      <template #header>
        <div class="alerts-header">
          <span>性能警告</span>
          <el-button size="small" @click="clearAllAlerts">
            <el-icon><Delete /></el-icon>
            清除所有
          </el-button>
        </div>
      </template>
      
      <div class="alerts-list">
        <el-alert
          v-for="(alert, index) in performanceAlerts"
          :key="index"
          :title="alert.title"
          :description="alert.description"
          :type="alert.type"
          :closable="true"
          @close="removeAlert(index)"
          style="margin-bottom: 12px;"
        >
          <template #default>
            <div class="alert-content">
              <div class="alert-time">{{ formatTime(alert.timestamp) }}</div>
              <div class="alert-metric">{{ alert.metric }}</div>
            </div>
          </template>
        </el-alert>
      </div>
    </el-card>

    <!-- 性能历史记录 -->
    <el-card class="history-card">
      <template #header>
        <div class="history-header">
          <span>性能历史记录</span>
          <div class="history-controls">
            <el-date-picker
              v-model="historyDateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              size="small"
              @change="loadHistoryData"
            />
            <el-button size="small" @click="exportHistoryData">
              <el-icon><Download /></el-icon>
              导出
            </el-button>
          </div>
        </div>
      </template>

      <el-table :data="performanceHistory" style="width: 100%">
        <el-table-column prop="timestamp" label="时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.timestamp) }}
          </template>
        </el-table-column>
        <el-table-column prop="cpu" label="CPU使用率" width="120">
          <template #default="{ row }">
            <el-tag :type="getMetricTagType(row.cpu)">{{ row.cpu }}%</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="memory" label="内存使用率" width="120">
          <template #default="{ row }">
            <el-tag :type="getMetricTagType(row.memory)">{{ row.memory }}%</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="disk" label="磁盘使用率" width="120">
          <template #default="{ row }">
            <el-tag :type="getMetricTagType(row.disk)">{{ row.disk }}%</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="network" label="网络延迟" width="120">
          <template #default="{ row }">
            {{ row.network }}ms
          </template>
        </el-table-column>
        <el-table-column prop="load" label="系统负载" width="120">
          <template #default="{ row }">
            {{ row.load }}
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getOverallStatusType(row)">
              {{ getOverallStatus(row) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="{ row, $index }">
            <el-button size="small" text @click="viewHistoryDetails(row)">
              <el-icon><View /></el-icon>
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="history-pagination">
        <el-pagination
          v-model:current-page="historyPagination.currentPage"
          v-model:page-size="historyPagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="historyTotal"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleHistorySizeChange"
          @current-change="handleHistoryCurrentChange"
        />
      </div>
    </el-card>

    <!-- 高级配置对话框 -->
    <el-dialog
      v-model="showAdvancedConfig"
      title="性能监控高级配置"
      width="900px"
      :before-close="handleAdvancedConfigClose"
    >
      <el-form :model="advancedConfig" ref="advancedConfigFormRef" label-width="140px">
        <el-divider content-position="left">监控配置</el-divider>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="启用性能监控">
              <el-switch v-model="advancedConfig.enableMonitoring" />
              <span style="margin-left: 8px; color: #999;">开启系统性能实时监控</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数据刷新间隔" prop="refreshInterval">
              <el-select v-model="advancedConfig.refreshInterval" placeholder="选择刷新间隔">
                <el-option label="5秒" :value="5" />
                <el-option label="10秒" :value="10" />
                <el-option label="30秒" :value="30" />
                <el-option label="1分钟" :value="60" />
                <el-option label="5分钟" :value="300" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="监控指标">
          <el-checkbox-group v-model="advancedConfig.monitoringMetrics">
            <el-checkbox label="cpu">CPU使用率</el-checkbox>
            <el-checkbox label="memory">内存使用率</el-checkbox>
            <el-checkbox label="disk">磁盘使用率</el-checkbox>
            <el-checkbox label="network">网络状态</el-checkbox>
            <el-checkbox label="database">数据库性能</el-checkbox>
            <el-checkbox label="api">API响应时间</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-divider content-position="left">告警设置</el-divider>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="CPU告警阈值" prop="cpuThreshold">
              <el-input-number v-model="advancedConfig.alertThresholds.cpu" :min="50" :max="95" />
              <span style="margin-left: 8px;">%</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="内存告警阈值" prop="memoryThreshold">
              <el-input-number v-model="advancedConfig.alertThresholds.memory" :min="50" :max="95" />
              <span style="margin-left: 8px;">%</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="磁盘告警阈值" prop="diskThreshold">
              <el-input-number v-model="advancedConfig.alertThresholds.disk" :min="70" :max="95" />
              <span style="margin-left: 8px;">%</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="响应时间告警" prop="responseTimeThreshold">
              <el-input-number v-model="advancedConfig.alertThresholds.responseTime" :min="100" :max="5000" />
              <span style="margin-left: 8px;">ms</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="告警通知方式">
          <el-checkbox-group v-model="advancedConfig.alertMethods">
            <el-checkbox label="email">邮件通知</el-checkbox>
            <el-checkbox label="sms">短信通知</el-checkbox>
            <el-checkbox label="webhook">Webhook</el-checkbox>
            <el-checkbox label="browser">浏览器通知</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-divider content-position="left">历史数据</el-divider>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="数据保留期">
              <el-select v-model="advancedConfig.dataRetentionDays" placeholder="选择保留期">
                <el-option label="7天" :value="7" />
                <el-option label="30天" :value="30" />
                <el-option label="90天" :value="90" />
                <el-option label="180天" :value="180" />
                <el-option label="1年" :value="365" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="数据采样间隔">
              <el-select v-model="advancedConfig.samplingInterval" placeholder="选择采样间隔">
                <el-option label="1分钟" :value="1" />
                <el-option label="5分钟" :value="5" />
                <el-option label="15分钟" :value="15" />
                <el-option label="1小时" :value="60" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="操作">
              <el-button type="primary" @click="exportPerformanceData" size="small">导出数据</el-button>
              <el-button type="warning" @click="clearPerformanceData" size="small">清理数据</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="resetAdvancedConfig">重置</el-button>
          <el-button @click="showAdvancedConfig = false">取消</el-button>
          <el-button type="primary" @click="saveAdvancedConfig" :loading="advancedConfigSaving">保存配置</el-button>
          <el-button type="info" @click="testAlertSettings">测试告警</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed, watch } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh, Download, ArrowUp, ArrowDown, Delete, View,
  Monitor, DataBoard, TrendCharts, Connection, Setting,
  User, Warning, CircleCheck, CircleClose
} from '@element-plus/icons-vue'
import { LineChart, DoughnutChart } from '@/components/charts'
import { usePerformanceStore } from '@/stores/performance'
import type { MetricCard } from '@/types/performance'

// 使用性能监控store和路由
const store = usePerformanceStore()
const route = useRoute()

// 响应式数据
const exporting = ref(false)
const showCpuDetails = ref(false)
const networkTimeRange = ref('6h')
const historyDateRange = ref<[Date, Date] | null>(null)

// 配置
const config = reactive({
  refreshInterval: 10,
  dataRetention: '6h',
  alertThreshold: 80
})

// 高级配置相关
const showAdvancedConfig = ref(false)
const advancedConfigSaving = ref(false)
const advancedConfigFormRef = ref()

const advancedConfig = reactive({
  enableMonitoring: true,
  refreshInterval: 30,
  monitoringMetrics: ['cpu', 'memory', 'disk', 'network', 'api'],
  alertThresholds: {
    cpu: 80,
    memory: 85,
    disk: 90,
    responseTime: 1000
  },
  alertMethods: ['email', 'browser'],
  dataRetentionDays: 30,
  samplingInterval: 5
})

// 性能指标数据
const performanceMetrics = reactive([
  {
    label: 'CPU使用率',
    value: 45,
    unit: '%',
    change: 2.3,
    color: '#3b82f6',
    icon: 'Monitor'
  },
  {
    label: '内存使用',
    value: 68,
    unit: '%',
    change: -1.2,
    color: '#10b981',
    icon: 'DataBoard'
  },
  {
    label: '磁盘I/O',
    value: 23,
    unit: '%',
    change: 5.7,
    color: '#f59e0b',
    icon: 'TrendCharts'
  },
  {
    label: '网络延迟',
    value: 28,
    unit: 'ms',
    change: -8.4,
    color: '#8b5cf6',
    icon: 'Connection'
  }
])

// CPU图表数据
const cpuChartData = ref({
  labels: [] as string[],
  datasets: [
    {
      label: 'CPU使用率',
      data: [] as number[],
      borderColor: '#3b82f6',
      backgroundColor: 'rgba(59, 130, 246, 0.1)',
      tension: 0.4,
      fill: true
    }
  ]
})

const cpuChartOptions = ref({
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false
    }
  },
  scales: {
    y: {
      beginAtZero: true,
      max: 100,
      title: {
        display: true,
        text: '使用率 (%)'
      }
    },
    x: {
      title: {
        display: true,
        text: '时间'
      }
    }
  }
})

// 内存图表数据
const memoryChartData = ref({
  labels: ['已使用', '缓存', '缓冲区', '可用'],
  datasets: [{
    data: [4.2, 1.8, 0.8, 1.2],
    backgroundColor: [
      '#ef4444',
      '#f59e0b',
      '#3b82f6',
      '#10b981'
    ],
    borderWidth: 2,
    borderColor: '#ffffff'
  }]
})

const memoryChartOptions = ref({
  responsive: true,
  maintainAspectRatio: false,
  cutout: '60%',
  plugins: {
    legend: {
      display: false
    }
  }
})

const memoryDetails = ref([
  { label: '已使用', value: '4.2GB', color: '#ef4444' },
  { label: '缓存', value: '1.8GB', color: '#f59e0b' },
  { label: '缓冲区', value: '0.8GB', color: '#3b82f6' },
  { label: '可用', value: '1.2GB', color: '#10b981' }
])

// 网络图表数据
const networkChartData = ref({
  labels: [] as string[],
  datasets: [
    {
      label: '入站流量',
      data: [] as number[],
      borderColor: '#10b981',
      backgroundColor: 'rgba(16, 185, 129, 0.2)',
      tension: 0.4,
      fill: true
    },
    {
      label: '出站流量',
      data: [] as number[],
      borderColor: '#3b82f6',
      backgroundColor: 'rgba(59, 130, 246, 0.2)',
      tension: 0.4,
      fill: true
    }
  ]
})

const networkChartOptions = ref({
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top' as const
    }
  },
  scales: {
    y: {
      beginAtZero: true,
      title: {
        display: true,
        text: '流量 (MB/s)'
      }
    }
  }
})

// 系统负载数据
const systemLoads = ref([
  { label: '1分钟', value: 45 },
  { label: '5分钟', value: 38 },
  { label: '15分钟', value: 42 }
])

// 性能警告
const performanceAlerts = ref([
  {
    title: 'CPU使用率过高',
    description: 'CPU使用率已达到85%，建议检查系统负载',
    type: 'warning' as const,
    timestamp: new Date(),
    metric: 'CPU: 85%'
  },
  {
    title: '内存使用率警告',
    description: '内存使用率超过阈值，可能影响系统性能',
    type: 'error' as const,
    timestamp: new Date(Date.now() - 300000),
    metric: 'Memory: 92%'
  }
])

// 性能历史记录
const performanceHistory = ref([
  {
    timestamp: new Date(),
    cpu: 45,
    memory: 68,
    disk: 23,
    network: 28,
    load: 1.2
  },
  {
    timestamp: new Date(Date.now() - 300000),
    cpu: 52,
    memory: 71,
    disk: 19,
    network: 32,
    load: 1.5
  }
])

const historyPagination = reactive({
  currentPage: 1,
  pageSize: 20
})

const historyTotal = ref(100)

// 方法实现
const getMetricStatus = (value: number) => {
  if (value >= 90) return 'error'
  if (value >= 70) return 'warning'
  if (value >= 50) return 'normal'
  return 'excellent'
}





const getLoadColor = (value: number) => {
  if (value >= 80) return '#ef4444'
  if (value >= 60) return '#f59e0b'
  return '#10b981'
}

const getMetricTagType = (value: number) => {
  if (value >= 90) return 'danger'
  if (value >= 70) return 'warning'
  return 'success'
}

const getOverallStatus = (row: any) => {
  const maxValue = Math.max(row.cpu, row.memory, row.disk)
  if (maxValue >= 90) return '异常'
  if (maxValue >= 70) return '警告'
  return '正常'
}

const getOverallStatusType = (row: any) => {
  const status = getOverallStatus(row)
  switch (status) {
    case '异常':
      return 'danger'
    case '警告':
      return 'warning'
    default:
      return 'success'
  }
}

const formatTime = (time: Date) => {
  return time.toLocaleString()
}

// 事件处理方法
const refreshPerformanceData = async () => {
  loading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1500))

    // 更新性能指标
    performanceMetrics.forEach(metric => {
      const oldValue = metric.value
      if (metric.unit === '%') {
        metric.value = Math.floor(Math.random() * 100)
      } else if (metric.unit === 'ms') {
        metric.value = Math.floor(Math.random() * 100) + 10
      }
      metric.change = ((metric.value - oldValue) / oldValue * 100)
    })

    // 更新图表数据
    updateChartData()

    ElMessage.success('性能数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  } finally {
    loading.value = false
  }
}

const exportPerformanceReport = async () => {
  exporting.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 模拟导出报告
    const reportData = {
      timestamp: new Date().toISOString(),
      metrics: performanceMetrics,
      history: performanceHistory.value,
      alerts: performanceAlerts.value
    }

    const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `performance-report-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    ElMessage.success('性能报告导出成功')
  } catch (error) {
    ElMessage.error('报告导出失败')
  } finally {
    exporting.value = false
  }
}

const toggleRealTime = (enabled: boolean) => {
  if (enabled) {
    startRealTimeMonitoring()
    ElMessage.success('实时监控已开启')
  } else {
    stopRealTimeMonitoring()
    ElMessage.info('实时监控已暂停')
  }
}

const updateRefreshInterval = () => {
  if (realTimeEnabled.value) {
    stopRealTimeMonitoring()
    startRealTimeMonitoring()
  }
  ElMessage.success(`刷新间隔已更新为${config.refreshInterval}秒`)
}

const toggleCpuChart = () => {
  showCpuDetails.value = !showCpuDetails.value
  // 可以在这里切换详细视图和简化视图的数据
  ElMessage.info(showCpuDetails.value ? '切换到详细视图' : '切换到简化视图')
}

const updateNetworkChart = () => {
  generateNetworkData()
  ElMessage.info(`网络图表已更新为${networkTimeRange.value}数据`)
}

const clearAllAlerts = () => {
  performanceAlerts.value = []
  ElMessage.success('所有警告已清除')
}

const removeAlert = (index: number) => {
  performanceAlerts.value.splice(index, 1)
  ElMessage.success('警告已移除')
}

const loadHistoryData = () => {
  // 根据时间范围加载历史数据
  ElMessage.info('正在加载历史数据...')
  // TODO: 实现实际的历史数据加载逻辑
}

const exportHistoryData = async () => {
  try {
    const blob = new Blob([JSON.stringify(performanceHistory.value, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `performance-history-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    ElMessage.success('历史数据导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

const viewHistoryDetails = (row: any) => {
  ElMessage.info(`查看${formatTime(row.timestamp)}的详细性能数据`)
  // TODO: 可以打开详情对话框显示更多信息
}

const handleHistorySizeChange = (size: number) => {
  historyPagination.pageSize = size
  historyPagination.currentPage = 1
}

const handleHistoryCurrentChange = (page: number) => {
  historyPagination.currentPage = page
}

// 数据生成方法
const updateChartData = () => {
  generateCpuData()
  generateNetworkData()
  updateSystemLoads()
}

const generateCpuData = () => {
  const labels = []
  const data = []
  const now = new Date()

  for (let i = 29; i >= 0; i--) {
    const time = new Date(now.getTime() - i * 60000) // 每分钟一个数据点
    labels.push(time.toLocaleTimeString())
    data.push(Math.floor(Math.random() * 100))
  }

  cpuChartData.value.labels = labels
  cpuChartData.value.datasets[0].data = data
}

const generateNetworkData = () => {
  const labels = []
  const inboundData = []
  const outboundData = []
  const now = new Date()

  const points = networkTimeRange.value === '1h' ? 12 : networkTimeRange.value === '6h' ? 36 : 144
  const interval = networkTimeRange.value === '1h' ? 300000 : networkTimeRange.value === '6h' ? 600000 : 600000

  for (let i = points - 1; i >= 0; i--) {
    const time = new Date(now.getTime() - i * interval)
    labels.push(time.toLocaleTimeString())
    inboundData.push(Math.random() * 10)
    outboundData.push(Math.random() * 8)
  }

  networkChartData.value.labels = labels
  networkChartData.value.datasets[0].data = inboundData
  networkChartData.value.datasets[1].data = outboundData
}

const updateSystemLoads = () => {
  systemLoads.value.forEach(load => {
    load.value = Math.floor(Math.random() * 100)
  })
}

// 实时监控
let monitoringTimer: NodeJS.Timeout | null = null

const startRealTimeMonitoring = () => {
  if (monitoringTimer) return

  monitoringTimer = setInterval(() => {
    if (realTimeEnabled.value) {
      refreshPerformanceData()

      // 检查是否需要生成警告
      performanceMetrics.forEach(metric => {
        if (metric.unit === '%' && metric.value > config.alertThreshold) {
          const existingAlert = performanceAlerts.value.find(alert =>
            alert.metric.includes(metric.label)
          )

          if (!existingAlert) {
            performanceAlerts.value.unshift({
              title: `${metric.label}过高`,
              description: `${metric.label}已达到${metric.value}%，超过阈值${config.alertThreshold}%`,
              type: metric.value >= 90 ? 'error' : 'warning',
              timestamp: new Date(),
              metric: `${metric.label}: ${metric.value}%`
            })
          }
        }
      })

      // 限制警告数量
      if (performanceAlerts.value.length > 10) {
        performanceAlerts.value = performanceAlerts.value.slice(0, 10)
      }
    }
  }, config.refreshInterval * 1000)
}

const stopRealTimeMonitoring = () => {
  if (monitoringTimer) {
    clearInterval(monitoringTimer)
    monitoringTimer = null
  }
}

// 高级配置相关方法
const handleAdvancedConfigClose = () => {
  showAdvancedConfig.value = false
}

const saveAdvancedConfig = async () => {
  try {
    advancedConfigSaving.value = true
    // 模拟保存配置
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 更新基础配置
    config.refreshInterval = advancedConfig.refreshInterval

    ElMessage.success('性能监控配置保存成功')
    showAdvancedConfig.value = false
  } catch (error) {
    ElMessage.error('配置保存失败')
  } finally {
    advancedConfigSaving.value = false
  }
}

const resetAdvancedConfig = () => {
  advancedConfig.enableMonitoring = true
  advancedConfig.refreshInterval = 30
  advancedConfig.monitoringMetrics = ['cpu', 'memory', 'disk', 'network', 'api']
  advancedConfig.alertThresholds = {
    cpu: 80,
    memory: 85,
    disk: 90,
    responseTime: 1000
  }
  advancedConfig.alertMethods = ['email', 'browser']
  advancedConfig.dataRetentionDays = 30
  advancedConfig.samplingInterval = 5
  ElMessage.success('配置已重置')
}

const exportPerformanceData = async () => {
  try {
    // 模拟导出性能数据
    const data = {
      metrics: performanceMetrics,
      alerts: performanceAlerts.value,
      config: advancedConfig,
      exportTime: new Date().toISOString()
    }

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `performance-data-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    ElMessage.success('性能数据导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

const clearPerformanceData = async () => {
  try {
    await ElMessageBox.confirm('确定要清理历史性能数据吗？此操作不可恢复。', '确认清理', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 模拟清理数据
    performanceAlerts.value = []
    ElMessage.success('历史数据已清理')
  } catch {
    // 用户取消
  }
}

const testAlertSettings = async () => {
  try {
    // 模拟测试告警
    const testAlert = {
      title: '测试告警',
      description: '这是一条测试告警消息，用于验证告警配置是否正常工作',
      type: 'info' as const,
      timestamp: new Date(),
      metric: '测试指标: 100%'
    }

    performanceAlerts.value.unshift(testAlert)
    ElMessage.success('测试告警已发送')
  } catch (error) {
    ElMessage.error('测试告警失败')
  }
}

// 新增的方法
const refreshAll = async () => {
  try {
    await store.refreshAll()
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  }
}

const refreshFrontendMetrics = () => {
  store.frontendMetrics = store.getFrontendMetrics()
  ElMessage.success('前端性能数据已刷新')
}

const getHealthStatusType = () => {
  if (!store.systemHealth) return 'info'
  switch (store.systemHealth.overall) {
    case 'healthy':
      return 'success'
    case 'warning':
      return 'warning'
    case 'error':
      return 'danger'
    default:
      return 'info'
  }
}

const getHealthStatusIcon = () => {
  if (!store.systemHealth) return 'CircleCheck'
  switch (store.systemHealth.overall) {
    case 'healthy':
      return 'CircleCheck'
    case 'warning':
      return 'Warning'
    case 'error':
      return 'CircleClose'
    default:
      return 'CircleCheck'
  }
}

const formatUptime = (seconds: number) => {
  const days = Math.floor(seconds / 86400)
  const hours = Math.floor((seconds % 86400) / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)

  if (days > 0) {
    return `${days}天 ${hours}小时`
  } else if (hours > 0) {
    return `${hours}小时 ${minutes}分钟`
  } else {
    return `${minutes}分钟`
  }
}

const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const getProgressValue = (metric: MetricCard) => {
  if (metric.unit === '%') {
    return Math.min(metric.value as number, 100)
  } else if (metric.unit === 'ms') {
    // 网络延迟，将ms转换为百分比（假设500ms为100%）
    return Math.min((metric.value as number) / 5, 100)
  }
  return 0
}

const getProgressColor = (metric: MetricCard) => {
  switch (metric.status) {
    case 'excellent':
      return '#67c23a'
    case 'normal':
      return '#409eff'
    case 'warning':
      return '#e6a23c'
    case 'error':
      return '#f56c6c'
    default:
      return '#909399'
  }
}

// 钻取分析功能
const focusedMetric = ref<string | null>(null)

const handleDrillDown = () => {
  const focus = route.query.focus as string
  const metric = route.query.metric as string

  if (focus && metric) {
    focusedMetric.value = focus

    // 显示相关的详细信息
    ElMessage.info(`正在查看 ${metric} 的详细信息`)

    // 根据指标类型展开相应的详细视图
    switch (focus) {
      case 'cpu':
        showCpuDetails.value = true
        break
      case 'memory':
        // 可以添加内存详细视图
        break
      case 'disk':
        // 可以添加磁盘详细视图
        break
      case 'network':
        // 可以添加网络详细视图
        break
    }

    // 滚动到相应的图表区域
    setTimeout(() => {
      const chartElement = document.querySelector('.performance-charts')
      if (chartElement) {
        chartElement.scrollIntoView({ behavior: 'smooth' })
      }
    }, 500)
  }
}

// 监听路由变化
watch(() => route.query, () => {
  handleDrillDown()
}, { immediate: true })

// 生命周期
onMounted(async () => {
  try {
    await store.initialize()
    updateChartData()
    handleDrillDown() // 处理初始的钻取参数
  } catch (error) {
    console.error('Performance monitoring initialization failed:', error)
  }
})

onUnmounted(() => {
  store.$dispose()
})
</script>

<style scoped lang="scss">
@import '@/styles/design-system.scss';

.performance-monitoring {
  padding: var(--spacing-4);

  /* 控制面板 */
  .control-panel {
    margin-bottom: var(--spacing-6);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow-sm);

    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-4);

      h3 {
        margin: 0;
        font-size: var(--font-size-lg);
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
      }

      .panel-actions {
        display: flex;
        align-items: center;
        gap: var(--spacing-3);

        .el-button {
          border-radius: var(--radius-lg);
        }
      }
    }

    /* 系统健康状态概览 */
    .health-overview {
      margin-top: var(--spacing-4);
      padding: var(--spacing-4);
      background: var(--bg-secondary);
      border-radius: var(--radius-lg);

      .health-summary {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-2);

        .uptime {
          font-size: var(--font-size-sm);
          color: var(--text-secondary);
        }
      }

      .health-components {
        display: flex;
        gap: var(--spacing-3);
        flex-wrap: wrap;

        .health-component {
          display: flex;
          align-items: center;
          gap: var(--spacing-2);
          padding: var(--spacing-2) var(--spacing-3);
          background: white;
          border-radius: var(--radius-md);
          border: 1px solid var(--border-light);

          &.healthy {
            border-color: var(--color-success);
          }

          &.warning {
            border-color: var(--color-warning);
          }

          &.error {
            border-color: var(--color-danger);
          }

          .component-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
          }

          .component-info {
            .component-name {
              font-size: var(--font-size-sm);
              font-weight: var(--font-weight-medium);
              color: var(--text-primary);
            }

            .component-status {
              font-size: var(--font-size-xs);
              color: var(--text-secondary);
            }
          }
        }
      }
    }
  }

  /* 快速统计卡片 */
  .quick-stats-card {
    .quick-stats {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-3);

      .stat-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-3);

        .stat-icon {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
        }

        .stat-content {
          .stat-value {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
          }

          .stat-label {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
          }
        }
      }
    }
  }

  /* 前端性能卡片 */
  .frontend-performance-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .frontend-metrics {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-3);

      .metric-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--spacing-2) 0;
        border-bottom: 1px solid var(--border-light);

        &:last-child {
          border-bottom: none;
        }

        .metric-label {
          font-size: var(--font-size-sm);
          color: var(--text-secondary);
        }

        .metric-value {
          font-size: var(--font-size-sm);
          font-weight: var(--font-weight-medium);
          color: var(--text-primary);
        }
      }
    }

    .monitoring-config {
      .el-form {
        .el-form-item {
          margin-bottom: var(--spacing-4);
          margin-right: var(--spacing-6);

          .el-form-item__label {
            font-weight: var(--font-weight-medium);
            color: var(--text-primary);
          }

          .el-select,
          .el-input-number {
            border-radius: var(--radius-lg);
          }
        }
      }
    }
  }

  /* 性能指标 */
  .performance-metrics {
    margin-bottom: var(--spacing-6);

    .metric-card {
      border-radius: var(--radius-xl);
      border: 1px solid var(--border-light);
      box-shadow: var(--shadow-sm);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
      }

      &.excellent {
        border-left: 4px solid var(--success-color);
      }

      &.normal {
        border-left: 4px solid var(--primary-color);
      }

      &.warning {
        border-left: 4px solid var(--warning-color);
      }

      &.error {
        border-left: 4px solid var(--error-color);
      }

      .metric-content {
        display: flex;
        align-items: center;
        gap: var(--spacing-4);
        padding: var(--spacing-5);

        .metric-icon {
          width: 48px;
          height: 48px;
          border-radius: var(--radius-lg);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: var(--font-size-lg);
        }

        .metric-info {
          flex: 1;

          .metric-value {
            font-size: var(--font-size-xl);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            margin-bottom: var(--spacing-1);
          }

          .metric-label {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            margin-bottom: var(--spacing-2);
          }

          .metric-change {
            display: flex;
            align-items: center;
            gap: var(--spacing-1);
            font-size: var(--font-size-xs);
            font-weight: var(--font-weight-medium);

            &.positive {
              color: var(--success-color);
            }

            &.negative {
              color: var(--error-color);
            }
          }
        }
      }

      .metric-progress {
        padding: 0 var(--spacing-5) var(--spacing-3);

        .el-progress {
          .el-progress-bar__outer {
            background-color: var(--bg-light);
          }
        }
      }
    }
  }

  /* 性能图表 */
  .performance-charts {
    margin-bottom: var(--spacing-6);

    .chart-card {
      border-radius: var(--radius-xl);
      border: 1px solid var(--border-light);
      box-shadow: var(--shadow-sm);
      margin-bottom: var(--spacing-6);

      .el-card__header {
        border-bottom: 1px solid var(--border-light);

        .chart-header {
          display: flex;
          justify-content: space-between;
          align-items: center;

          span {
            font-size: var(--font-size-base);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
          }
        }
      }

      .chart-container {
        padding: var(--spacing-4);
        min-height: 300px;

        .memory-details {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: var(--spacing-3);
          margin-top: var(--spacing-4);

          .memory-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-2);

            .memory-color {
              width: 12px;
              height: 12px;
              border-radius: 50%;
            }

            .memory-info {
              .memory-label {
                font-size: var(--font-size-xs);
                color: var(--text-secondary);
              }

              .memory-value {
                font-size: var(--font-size-sm);
                font-weight: var(--font-weight-medium);
                color: var(--text-primary);
              }
            }
          }
        }

        .load-gauges {
          display: flex;
          justify-content: space-around;
          align-items: center;
          padding: var(--spacing-4);

          .load-gauge {
            text-align: center;

            .gauge-container {
              margin-bottom: var(--spacing-2);

              .gauge-text {
                font-size: var(--font-size-sm);
                font-weight: var(--font-weight-bold);
                color: var(--text-primary);
              }
            }

            .gauge-label {
              font-size: var(--font-size-xs);
              color: var(--text-secondary);
              font-weight: var(--font-weight-medium);
            }
          }
        }
      }
    }
  }

  /* 警告卡片 */
  .alerts-card {
    margin-bottom: var(--spacing-6);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow-sm);

    .el-card__header {
      border-bottom: 1px solid var(--border-light);

      .alerts-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        span {
          font-size: var(--font-size-base);
          font-weight: var(--font-weight-semibold);
          color: var(--text-primary);
        }
      }
    }

    .alerts-list {
      .alert-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: var(--spacing-2);

        .alert-time {
          font-size: var(--font-size-xs);
          color: var(--text-secondary);
        }

        .alert-metric {
          font-size: var(--font-size-xs);
          font-weight: var(--font-weight-medium);
          color: var(--text-primary);
        }
      }
    }
  }

  /* 历史记录卡片 */
  .history-card {
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow-sm);

    .el-card__header {
      border-bottom: 1px solid var(--border-light);

      .history-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        span {
          font-size: var(--font-size-base);
          font-weight: var(--font-weight-semibold);
          color: var(--text-primary);
        }

        .history-controls {
          display: flex;
          align-items: center;
          gap: var(--spacing-3);
        }
      }
    }

    .history-pagination {
      padding: var(--spacing-4);
      border-top: 1px solid var(--border-light);
      display: flex;
      justify-content: center;
    }
  }
}

/* 响应式设计 */
@include respond-to('lg') {
  .performance-monitoring {
    .control-panel {
      .panel-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-3);
      }

      .monitoring-config .el-form .el-form-item {
        margin-right: 0;
        margin-bottom: var(--spacing-4);
      }
    }

    .performance-metrics .el-col {
      margin-bottom: var(--spacing-4);
    }

    .performance-charts .chart-card .chart-container .memory-details {
      grid-template-columns: 1fr;
    }
  }
}

@include respond-to('md') {
  .performance-monitoring {
    padding: var(--spacing-3);

    .performance-charts .chart-card .chart-container .load-gauges {
      flex-direction: column;
      gap: var(--spacing-4);

      .load-gauge .gauge-container {
        .el-progress {
          width: 60px !important;
          height: 60px !important;
        }
      }
    }

    .alerts-card .alerts-list .alert-content {
      flex-direction: column;
      align-items: flex-start;
      gap: var(--spacing-1);
    }

    .history-card .el-card__header .history-header {
      flex-direction: column;
      align-items: flex-start;
      gap: var(--spacing-3);

      .history-controls {
        width: 100%;
        justify-content: space-between;
      }
    }
  }
}

@include respond-to('sm') {
  .performance-monitoring {
    .control-panel {
      .monitoring-config .el-form {
        .el-form-item {
          width: 100%;

          .el-select,
          .el-input-number {
            width: 100%;
          }
        }
      }
    }

    .performance-charts .chart-card .chart-container .memory-details .memory-item {
      .memory-info {
        .memory-label,
        .memory-value {
          font-size: var(--font-size-xs);
        }
      }
    }

    .history-card {
      .el-table {
        font-size: var(--font-size-xs);
      }

      .history-pagination {
        .el-pagination {
          .el-pagination__sizes,
          .el-pagination__jump {
            display: none;
          }
        }
      }
    }
  }
}
</style>
