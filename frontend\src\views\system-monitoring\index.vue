<template>
  <div class="system-monitoring-overview">
    <!-- 系统健康状态 -->
    <el-card class="health-status-card">
      <template #header>
        <div class="card-header">
          <span>系统健康状态</span>
          <div class="header-actions">
            <el-tag :type="getHealthStatusType()" size="large">
              <el-icon><component :is="getHealthStatusIcon()" /></el-icon>
              {{ systemHealth.status }}
            </el-tag>
            <el-button size="small" @click="runHealthCheck" :loading="healthChecking">
              <el-icon><Refresh /></el-icon>
              健康检查
            </el-button>
          </div>
        </div>
      </template>

      <div class="health-overview">
        <el-row :gutter="24">
          <el-col :xs="24" :sm="12" :lg="8" v-for="(component, index) in systemHealth.components" :key="index">
            <div :class="['health-component', component.status]">
              <div class="component-icon" :style="{ backgroundColor: component.color }">
                <el-icon><component :is="component.icon" /></el-icon>
              </div>
              <div class="component-info">
                <div class="component-name">{{ component.name }}</div>
                <div class="component-status">{{ component.statusText }}</div>
                <div class="component-details">{{ component.details }}</div>
              </div>
              <div class="component-indicator">
                <el-icon><component :is="getComponentStatusIcon(component.status)" /></el-icon>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>



    <!-- 关键指标仪表板 -->
    <div class="metrics-dashboard">
      <el-row :gutter="24">
        <el-col :xs="24" :lg="16">
          <el-card class="metrics-chart-card">
            <template #header>
              <div class="chart-header">
                <span>系统性能趋势</span>
                <el-select v-model="metricsTimeRange" size="small" @change="updateMetricsChart">
                  <el-option label="最近1小时" value="1h" />
                  <el-option label="最近6小时" value="6h" />
                  <el-option label="最近24小时" value="24h" />
                </el-select>
              </div>
            </template>
            <div class="chart-container">
              <LineChart
                :data="metricsChartData"
                :options="metricsChartOptions"
                height="350px"
              />
            </div>
          </el-card>
        </el-col>

        <el-col :xs="24" :lg="8">
          <el-card class="quick-stats-card">
            <template #header>
              <span>快速统计</span>
            </template>
            <div class="quick-stats">
              <div v-for="(stat, index) in quickStats" :key="index" class="stat-item">
                <div class="stat-icon" :style="{ backgroundColor: stat.color }">
                  <el-icon><component :is="stat.icon" /></el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-value">{{ stat.value }}</div>
                  <div class="stat-label">{{ stat.label }}</div>
                  <div class="stat-change" :class="stat.trend >= 0 ? 'positive' : 'negative'">
                    <el-icon><ArrowUp v-if="stat.trend >= 0" /><ArrowDown v-else /></el-icon>
                    {{ Math.abs(stat.trend) }}%
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 最近活动和警告 -->
    <el-row :gutter="24">
      <el-col :xs="24" :lg="12">
        <el-card class="recent-activities-card">
          <template #header>
            <div class="card-header">
              <span>最近活动</span>
              <el-button size="small" text @click="viewAllActivities">
                查看全部
                <el-icon><ArrowRight /></el-icon>
              </el-button>
            </div>
          </template>
          
          <div class="activities-list">
            <div v-for="(activity, index) in recentActivities" :key="index" class="activity-item">
              <div class="activity-time">{{ formatTime(activity.timestamp) }}</div>
              <div class="activity-content">
                <div class="activity-icon" :class="activity.type">
                  <el-icon><component :is="activity.icon" /></el-icon>
                </div>
                <div class="activity-details">
                  <div class="activity-title">{{ activity.title }}</div>
                  <div class="activity-description">{{ activity.description }}</div>
                </div>
              </div>
            </div>
            
            <div v-if="recentActivities.length === 0" class="empty-activities">
              <el-empty description="暂无活动记录" />
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :lg="12">
        <el-card class="system-alerts-card">
          <template #header>
            <div class="card-header">
              <span>系统警告</span>
              <el-badge :value="systemAlerts.length" :hidden="systemAlerts.length === 0">
                <el-button size="small" text @click="viewAllAlerts">
                  查看全部
                  <el-icon><ArrowRight /></el-icon>
                </el-button>
              </el-badge>
            </div>
          </template>
          
          <div class="alerts-list">
            <div v-for="(alert, index) in systemAlerts.slice(0, 5)" :key="index" class="alert-item">
              <div class="alert-severity" :class="alert.severity">
                <el-icon><component :is="getAlertIcon(alert.severity)" /></el-icon>
              </div>
              <div class="alert-content">
                <div class="alert-title">{{ alert.title }}</div>
                <div class="alert-description">{{ alert.description }}</div>
                <div class="alert-time">{{ formatTime(alert.timestamp) }}</div>
              </div>
              <div class="alert-actions">
                <el-button size="small" text @click="resolveAlert(index)">
                  <el-icon><Check /></el-icon>
                </el-button>
                <el-button size="small" text @click="dismissAlert(index)">
                  <el-icon><Close /></el-icon>
                </el-button>
              </div>
            </div>
            
            <div v-if="systemAlerts.length === 0" class="empty-alerts">
              <el-empty description="暂无系统警告" />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 系统信息概览 -->
    <el-card class="system-info-card">
      <template #header>
        <span>系统信息概览</span>
      </template>
      
      <div class="system-info-grid">
        <el-row :gutter="24">
          <el-col :xs="24" :sm="12" :lg="6" v-for="(info, index) in systemInfo" :key="index">
            <div class="info-item">
              <div class="info-label">{{ info.label }}</div>
              <div class="info-value">{{ info.value }}</div>
              <div v-if="info.description" class="info-description">{{ info.description }}</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 快速操作面板 -->
    <el-card class="quick-actions-card">
      <template #header>
        <span>快速操作</span>
      </template>
      
      <div class="quick-actions">
        <el-button
          v-for="(action, index) in quickActions"
          :key="index"
          :type="action.type"
          :loading="action.loading"
          @click="executeQuickAction(action)"
          class="action-button"
        >
          <el-icon><component :is="action.icon" /></el-icon>
          {{ action.label }}
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh, ArrowUp, ArrowDown, ArrowRight, Check, Close,
  CircleCheck, Warning, CircleClose, Monitor, DataBoard, 
  Connection, Document, TrendCharts, Delete, RefreshRight
} from '@element-plus/icons-vue'
import { LineChart } from '@/components/charts'


// 路由
const router = useRouter()

// 响应式数据
const healthChecking = ref(false)
const metricsTimeRange = ref('6h')

// 系统健康状态
const systemHealth = reactive({
  status: '良好',
  lastCheck: new Date(),
  components: [
    {
      name: '数据库',
      status: 'healthy',
      statusText: '正常',
      details: '连接正常，响应时间 < 50ms',
      color: '#10b981',
      icon: 'DataBoard'
    },
    {
      name: 'Web服务',
      status: 'healthy',
      statusText: '正常',
      details: '服务运行正常，负载均衡',
      color: '#10b981',
      icon: 'Monitor'
    },
    {
      name: '缓存服务',
      status: 'warning',
      statusText: '警告',
      details: '内存使用率较高 (78%)',
      color: '#f59e0b',
      icon: 'TrendCharts'
    },
    {
      name: '网络连接',
      status: 'healthy',
      statusText: '正常',
      details: '网络延迟 < 30ms',
      color: '#10b981',
      icon: 'Connection'
    },
    {
      name: '存储系统',
      status: 'healthy',
      statusText: '正常',
      details: '磁盘使用率 45%',
      color: '#10b981',
      icon: 'Document'
    },
    {
      name: '日志系统',
      status: 'error',
      statusText: '异常',
      details: '日志写入失败',
      color: '#ef4444',
      icon: 'Document'
    }
  ]
})

// 快速统计数据
const quickStats = reactive([
  {
    label: '在线用户',
    value: '1,234',
    trend: 8.5,
    color: '#3b82f6',
    icon: 'Monitor'
  },
  {
    label: 'API请求/分钟',
    value: '2,456',
    trend: 12.3,
    color: '#10b981',
    icon: 'TrendCharts'
  },
  {
    label: '错误率',
    value: '0.12%',
    trend: -15.7,
    color: '#ef4444',
    icon: 'Warning'
  },
  {
    label: '平均响应时间',
    value: '245ms',
    trend: -5.2,
    color: '#8b5cf6',
    icon: 'Connection'
  }
])

// 图表数据
const metricsChartData = ref({
  labels: [] as string[],
  datasets: [
    {
      label: 'CPU使用率',
      data: [] as number[],
      borderColor: '#3b82f6',
      backgroundColor: 'rgba(59, 130, 246, 0.1)',
      tension: 0.4
    },
    {
      label: '内存使用率',
      data: [] as number[],
      borderColor: '#10b981',
      backgroundColor: 'rgba(16, 185, 129, 0.1)',
      tension: 0.4
    },
    {
      label: '磁盘使用率',
      data: [] as number[],
      borderColor: '#f59e0b',
      backgroundColor: 'rgba(245, 158, 11, 0.1)',
      tension: 0.4
    }
  ]
})

const metricsChartOptions = ref({
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top' as const
    }
  },
  scales: {
    y: {
      beginAtZero: true,
      max: 100,
      title: {
        display: true,
        text: '使用率 (%)'
      }
    }
  }
})

// 最近活动数据
const recentActivities = ref([
  {
    timestamp: new Date(),
    title: '系统重启完成',
    description: '系统维护重启已完成，所有服务正常运行',
    type: 'success',
    icon: 'CircleCheck'
  },
  {
    timestamp: new Date(Date.now() - 300000),
    title: '数据库备份',
    description: '定时数据库备份任务执行成功',
    type: 'info',
    icon: 'DataBoard'
  },
  {
    timestamp: new Date(Date.now() - 600000),
    title: '用户登录异常',
    description: '检测到异常登录尝试，已自动阻止',
    type: 'warning',
    icon: 'Warning'
  },
  {
    timestamp: new Date(Date.now() - 900000),
    title: '性能优化',
    description: '系统性能优化任务完成，响应速度提升15%',
    type: 'success',
    icon: 'TrendCharts'
  }
])

// 系统警告数据
const systemAlerts = ref([
  {
    title: 'CPU使用率过高',
    description: 'CPU使用率持续超过80%，建议检查系统负载',
    severity: 'warning',
    timestamp: new Date()
  },
  {
    title: '磁盘空间不足',
    description: '系统磁盘剩余空间不足10%，请及时清理',
    severity: 'error',
    timestamp: new Date(Date.now() - 600000)
  },
  {
    title: '内存使用率警告',
    description: '内存使用率达到75%，建议监控内存泄漏',
    severity: 'warning',
    timestamp: new Date(Date.now() - 1200000)
  }
])

// 系统信息
const systemInfo = reactive([
  {
    label: '系统版本',
    value: 'WisCude v1.0.0',
    description: '最新稳定版本'
  },
  {
    label: '运行时间',
    value: '15天 8小时',
    description: '自上次重启'
  },
  {
    label: '总内存',
    value: '16.0 GB',
    description: '系统总内存'
  },
  {
    label: '可用内存',
    value: '4.2 GB',
    description: '当前可用内存'
  },
  {
    label: '磁盘总量',
    value: '500 GB',
    description: '系统磁盘总容量'
  },
  {
    label: '磁盘可用',
    value: '285 GB',
    description: '当前可用空间'
  },
  {
    label: 'CPU核心',
    value: '8 核心',
    description: 'Intel i7-9700K'
  },
  {
    label: '网络状态',
    value: '正常',
    description: '网络连接稳定'
  }
])

// 快速操作
const quickActions = reactive([
  {
    label: '重启系统',
    type: 'danger',
    icon: 'RefreshRight',
    loading: false,
    action: 'restart'
  },
  {
    label: '清理缓存',
    type: 'warning',
    icon: 'Delete',
    loading: false,
    action: 'clear-cache'
  },
  {
    label: '备份数据',
    type: 'primary',
    icon: 'Document',
    loading: false,
    action: 'backup'
  },
  {
    label: '性能检测',
    type: 'info',
    icon: 'TrendCharts',
    loading: false,
    action: 'performance-check'
  }
])

// 方法实现
const getHealthStatusType = () => {
  const hasError = systemHealth.components.some(c => c.status === 'error')
  const hasWarning = systemHealth.components.some(c => c.status === 'warning')

  if (hasError) return 'danger'
  if (hasWarning) return 'warning'
  return 'success'
}

const getHealthStatusIcon = () => {
  const type = getHealthStatusType()
  switch (type) {
    case 'danger':
      return 'CircleClose'
    case 'warning':
      return 'Warning'
    default:
      return 'CircleCheck'
  }
}

const getComponentStatusIcon = (status: string) => {
  switch (status) {
    case 'healthy':
      return 'CircleCheck'
    case 'warning':
      return 'Warning'
    case 'error':
      return 'CircleClose'
    default:
      return 'CircleCheck'
  }
}

const getAlertIcon = (severity: string) => {
  switch (severity) {
    case 'error':
      return 'CircleClose'
    case 'warning':
      return 'Warning'
    default:
      return 'CircleCheck'
  }
}

const formatTime = (time: Date) => {
  return time.toLocaleString()
}

const runHealthCheck = async () => {
  healthChecking.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 模拟健康检查结果
    systemHealth.components.forEach(component => {
      const random = Math.random()
      if (random > 0.8) {
        component.status = 'error'
        component.statusText = '异常'
        component.color = '#ef4444'
      } else if (random > 0.6) {
        component.status = 'warning'
        component.statusText = '警告'
        component.color = '#f59e0b'
      } else {
        component.status = 'healthy'
        component.statusText = '正常'
        component.color = '#10b981'
      }
    })

    systemHealth.lastCheck = new Date()
    ElMessage.success('系统健康检查完成')
  } catch (error) {
    ElMessage.error('健康检查失败')
  } finally {
    healthChecking.value = false
  }
}

const updateMetricsChart = () => {
  generateMetricsData()
  ElMessage.info(`图表已更新为${metricsTimeRange.value}数据`)
}

const generateMetricsData = () => {
  const labels = []
  const cpuData = []
  const memoryData = []
  const diskData = []
  const now = new Date()

  const points = metricsTimeRange.value === '1h' ? 12 : metricsTimeRange.value === '6h' ? 36 : 144
  const interval = metricsTimeRange.value === '1h' ? 300000 : metricsTimeRange.value === '6h' ? 600000 : 600000

  for (let i = points - 1; i >= 0; i--) {
    const time = new Date(now.getTime() - i * interval)
    labels.push(time.toLocaleTimeString())
    cpuData.push(Math.floor(Math.random() * 100))
    memoryData.push(Math.floor(Math.random() * 100))
    diskData.push(Math.floor(Math.random() * 100))
  }

  metricsChartData.value.labels = labels
  metricsChartData.value.datasets[0].data = cpuData
  metricsChartData.value.datasets[1].data = memoryData
  metricsChartData.value.datasets[2].data = diskData
}

const viewAllActivities = () => {
  router.push('/system-monitoring/logs')
}

const viewAllAlerts = () => {
  ElMessage.info('跳转到系统警告页面')
}

const resolveAlert = (index: number) => {
  systemAlerts.value.splice(index, 1)
  ElMessage.success('警告已解决')
}

const dismissAlert = (index: number) => {
  systemAlerts.value.splice(index, 1)
  ElMessage.info('警告已忽略')
}

const executeQuickAction = async (action: any) => {
  action.loading = true

  try {
    switch (action.action) {
      case 'restart':
        await ElMessageBox.confirm('确定要重启系统吗？这将中断所有正在进行的操作。', '确认重启', {
          confirmButtonText: '确定重启',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await new Promise(resolve => setTimeout(resolve, 3000))
        ElMessage.success('系统重启请求已提交')
        break

      case 'clear-cache':
        await new Promise(resolve => setTimeout(resolve, 2000))
        ElMessage.success('系统缓存清理完成')
        break

      case 'backup':
        await new Promise(resolve => setTimeout(resolve, 4000))
        ElMessage.success('数据备份任务已启动')
        break

      case 'performance-check':
        await new Promise(resolve => setTimeout(resolve, 3000))
        ElMessage.success('性能检测完成')
        break
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(`操作失败: ${action.label}`)
    }
  } finally {
    action.loading = false
  }
}

// 生命周期
onMounted(() => {
  generateMetricsData()
})
</script>

<style scoped>
@import '@/styles/design-system.scss';

.system-monitoring-overview {
  padding: var(--spacing-4);



  /* 健康状态卡片 */
  .health-status-card {
    margin-bottom: var(--spacing-6);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow-sm);

    .el-card__header {
      border-bottom: 1px solid var(--border-light);

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        span {
          font-size: var(--font-size-lg);
          font-weight: var(--font-weight-semibold);
          color: var(--text-primary);
        }

        .header-actions {
          display: flex;
          align-items: center;
          gap: var(--spacing-3);
        }
      }
    }

    .health-overview {
      .health-component {
        display: flex;
        align-items: center;
        gap: var(--spacing-4);
        padding: var(--spacing-4);
        border-radius: var(--radius-lg);
        border: 1px solid var(--border-light);
        margin-bottom: var(--spacing-4);
        transition: all 0.3s ease;

        &:hover {
          box-shadow: var(--shadow-sm);
          transform: translateY(-2px);
        }

        &:last-child {
          margin-bottom: 0;
        }

        &.healthy {
          border-left: 4px solid var(--success-color);
        }

        &.warning {
          border-left: 4px solid var(--warning-color);
        }

        &.error {
          border-left: 4px solid var(--error-color);
        }

        .component-icon {
          width: 40px;
          height: 40px;
          border-radius: var(--radius-lg);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: var(--font-size-base);
        }

        .component-info {
          flex: 1;

          .component-name {
            font-size: var(--font-size-base);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            margin-bottom: var(--spacing-1);
          }

          .component-status {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            margin-bottom: var(--spacing-1);
          }

          .component-details {
            font-size: var(--font-size-xs);
            color: var(--text-tertiary);
          }
        }

        .component-indicator {
          font-size: var(--font-size-lg);

          &.healthy {
            color: var(--success-color);
          }

          &.warning {
            color: var(--warning-color);
          }

          &.error {
            color: var(--error-color);
          }
        }
      }
    }
  }

  /* 指标仪表板 */
  .metrics-dashboard {
    margin-bottom: var(--spacing-6);

    .metrics-chart-card,
    .quick-stats-card {
      border-radius: var(--radius-xl);
      border: 1px solid var(--border-light);
      box-shadow: var(--shadow-sm);

      .el-card__header {
        border-bottom: 1px solid var(--border-light);

        .chart-header {
          display: flex;
          justify-content: space-between;
          align-items: center;

          span {
            font-size: var(--font-size-base);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
          }
        }
      }

      .chart-container {
        padding: var(--spacing-4);
        min-height: 350px;
      }
    }

    .quick-stats-card {
      .quick-stats {
        .stat-item {
          display: flex;
          align-items: center;
          gap: var(--spacing-4);
          padding: var(--spacing-4);
          border-bottom: 1px solid var(--border-light);

          &:last-child {
            border-bottom: none;
          }

          .stat-icon {
            width: 36px;
            height: 36px;
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: var(--font-size-sm);
          }

          .stat-content {
            flex: 1;

            .stat-value {
              font-size: var(--font-size-lg);
              font-weight: var(--font-weight-bold);
              color: var(--text-primary);
              margin-bottom: var(--spacing-1);
            }

            .stat-label {
              font-size: var(--font-size-xs);
              color: var(--text-secondary);
              margin-bottom: var(--spacing-1);
            }

            .stat-change {
              display: flex;
              align-items: center;
              gap: var(--spacing-1);
              font-size: var(--font-size-xs);
              font-weight: var(--font-weight-medium);

              &.positive {
                color: var(--success-color);
              }

              &.negative {
                color: var(--error-color);
              }
            }
          }
        }
      }
    }
  }

  /* 最近活动和警告 */
  .recent-activities-card,
  .system-alerts-card {
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow-sm);
    margin-bottom: var(--spacing-6);

    .el-card__header {
      border-bottom: 1px solid var(--border-light);

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        span {
          font-size: var(--font-size-base);
          font-weight: var(--font-weight-semibold);
          color: var(--text-primary);
        }
      }
    }

    .activities-list,
    .alerts-list {
      max-height: 400px;
      overflow-y: auto;

      .activity-item,
      .alert-item {
        padding: var(--spacing-4);
        border-bottom: 1px solid var(--border-light);
        transition: all 0.2s ease;

        &:hover {
          background: var(--bg-light);
        }

        &:last-child {
          border-bottom: none;
        }
      }

      .activity-item {
        .activity-time {
          font-size: var(--font-size-xs);
          color: var(--text-tertiary);
          margin-bottom: var(--spacing-2);
        }

        .activity-content {
          display: flex;
          align-items: flex-start;
          gap: var(--spacing-3);

          .activity-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-xs);
            margin-top: 2px;

            &.success {
              background: var(--success-light);
              color: var(--success-color);
            }

            &.info {
              background: var(--primary-light);
              color: var(--primary-color);
            }

            &.warning {
              background: var(--warning-light);
              color: var(--warning-color);
            }

            &.error {
              background: var(--error-light);
              color: var(--error-color);
            }
          }

          .activity-details {
            flex: 1;

            .activity-title {
              font-size: var(--font-size-sm);
              font-weight: var(--font-weight-medium);
              color: var(--text-primary);
              margin-bottom: var(--spacing-1);
            }

            .activity-description {
              font-size: var(--font-size-xs);
              color: var(--text-secondary);
              line-height: 1.4;
            }
          }
        }
      }

      .alert-item {
        display: flex;
        align-items: flex-start;
        gap: var(--spacing-3);

        .alert-severity {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: var(--font-size-xs);
          margin-top: 2px;

          &.warning {
            background: var(--warning-light);
            color: var(--warning-color);
          }

          &.error {
            background: var(--error-light);
            color: var(--error-color);
          }
        }

        .alert-content {
          flex: 1;

          .alert-title {
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
            color: var(--text-primary);
            margin-bottom: var(--spacing-1);
          }

          .alert-description {
            font-size: var(--font-size-xs);
            color: var(--text-secondary);
            line-height: 1.4;
            margin-bottom: var(--spacing-1);
          }

          .alert-time {
            font-size: var(--font-size-xs);
            color: var(--text-tertiary);
          }
        }

        .alert-actions {
          display: flex;
          gap: var(--spacing-1);
          opacity: 0;
          transition: opacity 0.2s ease;

          .el-button {
            padding: 4px;
          }
        }

        &:hover .alert-actions {
          opacity: 1;
        }
      }

      .empty-activities,
      .empty-alerts {
        padding: var(--spacing-8);
        text-align: center;
      }
    }
  }

  /* 系统信息卡片 */
  .system-info-card {
    margin-bottom: var(--spacing-6);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow-sm);

    .el-card__header {
      border-bottom: 1px solid var(--border-light);

      span {
        font-size: var(--font-size-base);
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
      }
    }

    .system-info-grid {
      .info-item {
        padding: var(--spacing-4);
        border-radius: var(--radius-lg);
        border: 1px solid var(--border-light);
        margin-bottom: var(--spacing-4);
        transition: all 0.3s ease;

        &:hover {
          box-shadow: var(--shadow-sm);
          transform: translateY(-2px);
        }

        .info-label {
          font-size: var(--font-size-xs);
          color: var(--text-secondary);
          margin-bottom: var(--spacing-1);
          font-weight: var(--font-weight-medium);
        }

        .info-value {
          font-size: var(--font-size-base);
          font-weight: var(--font-weight-semibold);
          color: var(--text-primary);
          margin-bottom: var(--spacing-1);
        }

        .info-description {
          font-size: var(--font-size-xs);
          color: var(--text-tertiary);
        }
      }
    }
  }

  /* 快速操作卡片 */
  .quick-actions-card {
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow-sm);

    .el-card__header {
      border-bottom: 1px solid var(--border-light);

      span {
        font-size: var(--font-size-base);
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
      }
    }

    .quick-actions {
      display: flex;
      flex-wrap: wrap;
      gap: var(--spacing-3);

      .action-button {
        border-radius: var(--radius-lg);
        font-weight: var(--font-weight-medium);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: var(--shadow-md);
        }
      }
    }
  }
}

/* 响应式设计 */
@include respond-to('lg') {
  .system-monitoring-overview {
    .health-status-card .el-card__header .card-header {
      flex-direction: column;
      align-items: flex-start;
      gap: var(--spacing-3);
    }

    .metrics-dashboard .el-col {
      margin-bottom: var(--spacing-4);
    }

    .system-info-card .system-info-grid .el-col {
      margin-bottom: var(--spacing-4);
    }
  }
}

@include respond-to('md') {
  .system-monitoring-overview {
    padding: var(--spacing-3);

    .health-overview .health-component {
      flex-direction: column;
      text-align: center;
      gap: var(--spacing-2);

      .component-icon {
        width: 32px;
        height: 32px;
      }
    }

    .metrics-dashboard .quick-stats-card .quick-stats .stat-item {
      flex-direction: column;
      text-align: center;
      gap: var(--spacing-2);

      .stat-icon {
        width: 32px;
        height: 32px;
      }
    }

    .recent-activities-card .activities-list .activity-item .activity-content,
    .system-alerts-card .alerts-list .alert-item {
      flex-direction: column;
      align-items: flex-start;
      gap: var(--spacing-2);

      .alert-actions {
        opacity: 1;
        align-self: flex-end;
      }
    }
  }
}

@include respond-to('sm') {
  .system-monitoring-overview {
    .quick-actions-card .quick-actions {
      flex-direction: column;

      .action-button {
        width: 100%;
        justify-content: center;
      }
    }

    .system-info-card .system-info-grid .info-item {
      text-align: center;
    }
  }
}
</style>
