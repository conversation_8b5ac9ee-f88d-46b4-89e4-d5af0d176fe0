<!--
  轻量级性能概览组件
  用于在其他页面显示简化的性能指标
-->

<template>
  <el-card :class="['performance-overview', { 'clickable': clickable }]" @click="handleClick">
    <template #header v-if="showTitle">
      <div class="overview-header">
        <span>系统性能概览</span>
        <div class="header-actions">
          <el-tag v-if="store.overallStatus" :type="getStatusType()" size="small">
            {{ getStatusText() }}
          </el-tag>
          <el-button v-if="showRefresh" size="small" @click.stop="refreshData" :loading="refreshing">
            <el-icon><Refresh /></el-icon>
          </el-button>
        </div>
      </div>
    </template>

    <div class="metrics-grid">
      <div
        v-for="(metric, index) in displayMetrics"
        :key="index"
        :class="['metric-item', metric.status, { 'with-trend': showTrends, 'clickable': clickable }]"
        @click="handleMetricClick(metric)"
      >
        <div class="metric-icon" :style="{ backgroundColor: metric.color }">
          <el-icon><component :is="metric.icon" /></el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ metric.value }}{{ metric.unit }}</div>
          <div class="metric-label">{{ metric.label }}</div>
          <div v-if="showTrends && metric.change !== undefined" 
               :class="['metric-trend', metric.change >= 0 ? 'positive' : 'negative']">
            <el-icon><ArrowUp v-if="metric.change >= 0" /><ArrowDown v-else /></el-icon>
            <span>{{ Math.abs(metric.change).toFixed(1) }}%</span>
          </div>
        </div>
        <div class="metric-status">
          <el-icon><component :is="getMetricStatusIcon(metric.status)" /></el-icon>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="store.loading" class="loading-overlay">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>加载中...</span>
    </div>

    <!-- 错误状态 -->
    <div v-if="store.error && !store.loading" class="error-overlay">
      <el-icon><Warning /></el-icon>
      <span>{{ store.error }}</span>
      <el-button size="small" @click="refreshData">重试</el-button>
    </div>

    <!-- 最后更新时间 -->
    <div v-if="showLastUpdate && store.lastUpdate" class="last-update">
      最后更新: {{ formatTime(store.lastUpdate) }}
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Refresh, ArrowUp, ArrowDown, Warning, Loading,
  CircleCheck, CircleClose, Monitor, DataBoard, 
  TrendCharts, Connection
} from '@element-plus/icons-vue'
import { usePerformanceStore } from '@/stores/performance'
import type { MetricCard } from '@/types/performance'

// Props定义
interface Props {
  showTitle?: boolean        // 是否显示标题
  showTrends?: boolean       // 是否显示趋势
  showRefresh?: boolean      // 是否显示刷新按钮
  showLastUpdate?: boolean   // 是否显示最后更新时间
  clickable?: boolean        // 是否可点击跳转
  refreshInterval?: number   // 自动刷新间隔(秒)，0表示不自动刷新
  maxMetrics?: number        // 最大显示指标数量
  metricTypes?: string[]     // 指定显示的指标类型
}

const props = withDefaults(defineProps<Props>(), {
  showTitle: true,
  showTrends: true,
  showRefresh: true,
  showLastUpdate: true,
  clickable: true,
  refreshInterval: 0,
  maxMetrics: 4,
  metricTypes: () => ['cpu', 'memory', 'disk', 'network']
})

// 组合式API
const router = useRouter()
const store = usePerformanceStore()

// 响应式数据
const refreshing = ref(false)
let autoRefreshTimer: number | null = null

// 计算属性 - 显示的指标
const displayMetrics = computed((): MetricCard[] => {
  if (!store.metricCards || store.metricCards.length === 0) {
    return []
  }

  let metrics = store.metricCards

  // 根据指定类型过滤
  if (props.metricTypes.length > 0) {
    metrics = metrics.filter(metric => {
      const type = getMetricType(metric.label)
      return props.metricTypes.includes(type)
    })
  }

  // 限制数量
  if (props.maxMetrics > 0) {
    metrics = metrics.slice(0, props.maxMetrics)
  }

  return metrics
})

// 方法
const refreshData = async () => {
  if (refreshing.value) return

  try {
    refreshing.value = true
    await store.refreshAll()
  } catch (error) {
    ElMessage.error('刷新失败')
  } finally {
    refreshing.value = false
  }
}

const handleClick = () => {
  if (props.clickable) {
    router.push('/system-monitoring/performance')
  }
}

const handleMetricClick = (metric: MetricCard) => {
  if (props.clickable) {
    // 根据指标类型跳转到对应的详细页面，并传递参数
    const metricType = getMetricType(metric.label)
    router.push({
      path: '/system-monitoring/performance',
      query: {
        focus: metricType,
        metric: metric.label
      }
    })
  }
}

const getStatusType = () => {
  switch (store.overallStatus) {
    case 'healthy':
      return 'success'
    case 'warning':
      return 'warning'
    case 'error':
      return 'danger'
    default:
      return 'info'
  }
}

const getStatusText = () => {
  switch (store.overallStatus) {
    case 'healthy':
      return '正常'
    case 'warning':
      return '警告'
    case 'error':
      return '异常'
    default:
      return '未知'
  }
}

const getMetricStatusIcon = (status: string) => {
  switch (status) {
    case 'excellent':
    case 'normal':
      return 'CircleCheck'
    case 'warning':
      return 'Warning'
    case 'error':
      return 'CircleClose'
    default:
      return 'CircleCheck'
  }
}

const getMetricType = (label: string): string => {
  if (label.includes('CPU')) return 'cpu'
  if (label.includes('内存')) return 'memory'
  if (label.includes('磁盘')) return 'disk'
  if (label.includes('网络')) return 'network'
  return 'other'
}

const formatTime = (date: Date): string => {
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const startAutoRefresh = () => {
  if (props.refreshInterval > 0) {
    autoRefreshTimer = window.setInterval(() => {
      refreshData()
    }, props.refreshInterval * 1000)
  }
}

const stopAutoRefresh = () => {
  if (autoRefreshTimer) {
    clearInterval(autoRefreshTimer)
    autoRefreshTimer = null
  }
}

// 生命周期
onMounted(async () => {
  // 如果store还没有初始化，则初始化
  if (!store.metrics) {
    try {
      await store.initialize()
    } catch (error) {
      console.error('Performance overview initialization failed:', error)
    }
  }

  // 启动自动刷新
  startAutoRefresh()
})

onUnmounted(() => {
  stopAutoRefresh()
})

// 暴露方法给父组件
defineExpose({
  refresh: refreshData,
  startAutoRefresh,
  stopAutoRefresh
})
</script>

<style scoped>
.performance-overview {
  position: relative;
  border-radius: 12px;
  border: 1px solid var(--el-border-color-light);
  transition: all 0.3s ease;

  &.clickable {
    cursor: pointer;

    &:hover {
      border-color: var(--el-color-primary);
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
      transform: translateY(-2px);
    }
  }

  .overview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-actions {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;

    .metric-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px;
      background: var(--el-bg-color-page);
      border-radius: 8px;
      border: 1px solid transparent;
      transition: all 0.3s ease;

      &.excellent {
        border-color: var(--el-color-success);
        background: rgba(103, 194, 58, 0.05);
      }

      &.normal {
        border-color: var(--el-color-primary);
        background: rgba(64, 158, 255, 0.05);
      }

      &.warning {
        border-color: var(--el-color-warning);
        background: rgba(230, 162, 60, 0.05);
      }

      &.error {
        border-color: var(--el-color-danger);
        background: rgba(245, 108, 108, 0.05);
      }

      &.clickable {
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          border-color: var(--el-color-primary);
        }
      }

      .metric-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 18px;
        flex-shrink: 0;
      }

      .metric-content {
        flex: 1;

        .metric-value {
          font-size: 18px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          line-height: 1.2;
        }

        .metric-label {
          font-size: 12px;
          color: var(--el-text-color-regular);
          margin-top: 2px;
        }

        .metric-trend {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 11px;
          margin-top: 4px;

          &.positive {
            color: var(--el-color-danger);
          }

          &.negative {
            color: var(--el-color-success);
          }
        }
      }

      .metric-status {
        font-size: 16px;
        flex-shrink: 0;

        .el-icon {
          color: var(--el-color-success);
        }
      }
    }
  }

  .loading-overlay,
  .error-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 12px;
    gap: 8px;
    font-size: 14px;
    color: var(--el-text-color-regular);
  }

  .error-overlay {
    color: var(--el-color-danger);
  }

  .last-update {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid var(--el-border-color-lighter);
    font-size: 11px;
    color: var(--el-text-color-placeholder);
    text-align: center;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .performance-overview {
    .metrics-grid {
      grid-template-columns: 1fr;
    }

    .metric-item {
      .metric-icon {
        width: 32px;
        height: 32px;
        font-size: 14px;
      }

      .metric-content {
        .metric-value {
          font-size: 16px;
        }
      }
    }
  }
}
</style>
