<template>
  <div class="profile-page">
    <div class="page-header">
      <h1>个人资料</h1>
      <p>管理您的个人信息和账户设置</p>
    </div>

    <div class="profile-container">
      <div class="profile-sidebar">
        <el-card class="profile-card">
          <div class="avatar-section">
            <el-upload
              class="avatar-uploader"
              :action="uploadUrl"
              :headers="uploadHeaders"
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUpload"
            >
              <img v-if="userInfo.avatar" :src="userInfo.avatar" class="avatar" />
              <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
            </el-upload>
            <div class="avatar-info">
              <h3>{{ userInfo.username }}</h3>
              <p class="role-tag">{{ getRoleText(userInfo.role) }}</p>
              <p class="last-login">上次登录：{{ formatTime(userInfo.last_login) }}</p>
            </div>
          </div>
          
          <div class="stats-section">
            <div class="stat-item">
              <span class="stat-label">注册时间</span>
              <span class="stat-value">{{ formatDate(userInfo.created_at) }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">登录次数</span>
              <span class="stat-value">{{ userInfo.login_count || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">账户状态</span>
              <el-tag :type="userInfo.is_active ? 'success' : 'danger'">
                {{ userInfo.is_active ? '正常' : '已禁用' }}
              </el-tag>
            </div>
          </div>
        </el-card>
      </div>
      
      <div class="profile-content">
        <el-tabs v-model="activeTab" type="border-card">
          <!-- 基本信息 -->
          <el-tab-pane label="基本信息" name="basic">
            <el-form
              ref="basicFormRef"
              :model="basicForm"
              :rules="basicRules"
              label-width="120px"
              class="profile-form"
            >
              <el-form-item label="用户名" prop="username">
                <el-input
                  v-model="basicForm.username"
                  placeholder="请输入用户名"
                  :disabled="!canEditUsername"
                />
                <div class="form-tip">用户名用于登录，一旦设置后不建议修改</div>
              </el-form-item>
              
              <el-form-item label="真实姓名" prop="full_name">
                <el-input
                  v-model="basicForm.full_name"
                  placeholder="请输入真实姓名"
                />
              </el-form-item>
              
              <el-form-item label="邮箱地址" prop="email">
                <el-input
                  v-model="basicForm.email"
                  type="email"
                  placeholder="请输入邮箱地址"
                />
                <div class="form-tip">邮箱用于接收系统通知和密码重置</div>
              </el-form-item>
              
              <el-form-item label="手机号码" prop="phone">
                <el-input
                  v-model="basicForm.phone"
                  placeholder="请输入手机号码"
                />
              </el-form-item>
              
              <el-form-item label="部门" prop="department">
                <el-input
                  v-model="basicForm.department"
                  placeholder="请输入所属部门"
                />
              </el-form-item>
              
              <el-form-item label="职位" prop="position">
                <el-input
                  v-model="basicForm.position"
                  placeholder="请输入职位"
                />
              </el-form-item>
              
              <el-form-item label="个人简介" prop="bio">
                <el-input
                  v-model="basicForm.bio"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入个人简介"
                />
              </el-form-item>
              
              <el-form-item>
                <el-button
                  type="primary"
                  @click="saveBasicInfo"
                  :loading="saving"
                >
                  保存基本信息
                </el-button>
                <el-button @click="resetBasicForm">重置</el-button>
              </el-form-item>
            </el-form>
          </el-tab-pane>
          
          <!-- 安全设置 -->
          <el-tab-pane label="安全设置" name="security">
            <div class="security-section">
              <h4>修改密码</h4>
              <el-form
                ref="passwordFormRef"
                :model="passwordForm"
                :rules="passwordRules"
                label-width="120px"
                class="profile-form"
              >
                <el-form-item label="当前密码" prop="current_password">
                  <el-input
                    v-model="passwordForm.current_password"
                    type="password"
                    placeholder="请输入当前密码"
                    show-password
                  />
                </el-form-item>
                
                <el-form-item label="新密码" prop="new_password">
                  <el-input
                    v-model="passwordForm.new_password"
                    type="password"
                    placeholder="请输入新密码"
                    show-password
                    @input="checkPasswordStrength"
                  />
                  <div v-if="passwordStrength" class="password-strength">
                    <div class="strength-bar">
                      <div 
                        class="strength-fill"
                        :class="passwordStrength.level"
                        :style="{ width: passwordStrength.score + '%' }"
                      ></div>
                    </div>
                    <div class="strength-text">
                      密码强度：{{ passwordStrength.text }}
                    </div>
                  </div>
                </el-form-item>
                
                <el-form-item label="确认密码" prop="confirm_password">
                  <el-input
                    v-model="passwordForm.confirm_password"
                    type="password"
                    placeholder="请再次输入新密码"
                    show-password
                  />
                </el-form-item>
                
                <el-form-item>
                  <el-button
                    type="primary"
                    @click="changePassword"
                    :loading="changingPassword"
                  >
                    修改密码
                  </el-button>
                  <el-button @click="resetPasswordForm">重置</el-button>
                </el-form-item>
              </el-form>
            </div>
            
            <el-divider />
            
            <div class="security-section">
              <h4>双因素认证</h4>
              <div class="two-factor-auth">
                <div class="auth-status">
                  <el-icon><Lock /></el-icon>
                  <span>双因素认证状态：</span>
                  <el-tag :type="userInfo.two_factor_enabled ? 'success' : 'warning'">
                    {{ userInfo.two_factor_enabled ? '已启用' : '未启用' }}
                  </el-tag>
                </div>
                <div class="auth-actions">
                  <el-button
                    v-if="!userInfo.two_factor_enabled"
                    type="primary"
                    @click="enableTwoFactor"
                  >
                    启用双因素认证
                  </el-button>
                  <el-button
                    v-else
                    type="danger"
                    @click="disableTwoFactor"
                  >
                    禁用双因素认证
                  </el-button>
                </div>
                <div class="auth-description">
                  双因素认证为您的账户提供额外的安全保护
                </div>
              </div>
            </div>
            
            <el-divider />
            
            <div class="security-section">
              <h4>登录历史</h4>
              <el-table :data="loginHistory" style="width: 100%">
                <el-table-column prop="login_time" label="登录时间" width="180">
                  <template #default="{ row }">
                    {{ formatTime(row.login_time) }}
                  </template>
                </el-table-column>
                <el-table-column prop="ip_address" label="IP地址" width="150" />
                <el-table-column prop="user_agent" label="设备信息" />
                <el-table-column prop="location" label="登录地点" width="120" />
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag :type="row.status === 'success' ? 'success' : 'danger'">
                      {{ row.status === 'success' ? '成功' : '失败' }}
                    </el-tag>
                  </template>
                </el-table-column>
              </el-table>
              
              <div class="table-pagination">
                <el-pagination
                  v-model:current-page="loginHistoryPage"
                  v-model:page-size="loginHistoryPageSize"
                  :total="loginHistoryTotal"
                  :page-sizes="[10, 20, 50]"
                  layout="total, sizes, prev, pager, next, jumper"
                  @size-change="loadLoginHistory"
                  @current-change="loadLoginHistory"
                />
              </div>
            </div>
          </el-tab-pane>
          
          <!-- 偏好设置 -->
          <el-tab-pane label="偏好设置" name="preferences">
            <el-form
              ref="preferencesFormRef"
              :model="preferencesForm"
              label-width="120px"
              class="profile-form"
            >
              <el-form-item label="界面语言">
                <el-select v-model="preferencesForm.language" style="width: 200px">
                  <el-option label="简体中文" value="zh-CN" />
                  <el-option label="English" value="en-US" />
                </el-select>
              </el-form-item>
              
              <el-form-item label="时区设置">
                <el-select v-model="preferencesForm.timezone" style="width: 300px">
                  <el-option label="北京时间 (UTC+8)" value="Asia/Shanghai" />
                  <el-option label="东京时间 (UTC+9)" value="Asia/Tokyo" />
                  <el-option label="纽约时间 (UTC-5)" value="America/New_York" />
                  <el-option label="伦敦时间 (UTC+0)" value="Europe/London" />
                </el-select>
              </el-form-item>
              
              <el-form-item label="主题设置">
                <el-radio-group v-model="preferencesForm.theme">
                  <el-radio label="light">浅色主题</el-radio>
                  <el-radio label="dark">深色主题</el-radio>
                  <el-radio label="auto">跟随系统</el-radio>
                </el-radio-group>
              </el-form-item>
              
              <el-form-item label="邮件通知">
                <el-checkbox-group v-model="preferencesForm.email_notifications">
                  <el-checkbox label="system_updates">系统更新通知</el-checkbox>
                  <el-checkbox label="security_alerts">安全警报</el-checkbox>
                  <el-checkbox label="login_alerts">登录提醒</el-checkbox>
                  <el-checkbox label="weekly_reports">周报推送</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              
              <el-form-item label="默认页面大小">
                <el-input-number
                  v-model="preferencesForm.default_page_size"
                  :min="10"
                  :max="100"
                  :step="10"
                />
              </el-form-item>
              
              <el-form-item>
                <el-button
                  type="primary"
                  @click="savePreferences"
                  :loading="saving"
                >
                  保存偏好设置
                </el-button>
                <el-button @click="resetPreferencesForm">重置</el-button>
              </el-form-item>
            </el-form>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Lock, Edit, Delete, User, Search } from '@element-plus/icons-vue'
import { useAuthStore } from '@/store/auth'
import { profileApi } from '@/api/profile'
import { adminApi } from '@/api/admin'
import type { FormInstance, FormRules, UploadProps } from 'element-plus'
import type { AdminUser, AdminUserCreate, AdminUserUpdate } from '@/api/admin'

const authStore = useAuthStore()
const activeTab = ref('basic')
const saving = ref(false)
const changingPassword = ref(false)
const passwordStrength = ref<any>(null)

// 表单引用
const basicFormRef = ref<FormInstance>()
const passwordFormRef = ref<FormInstance>()
const preferencesFormRef = ref<FormInstance>()
const adminFormRef = ref<FormInstance>()

// 管理员管理相关数据
const adminUsers = ref<AdminUser[]>([])
const adminLoading = ref(false)
const adminDialogVisible = ref(false)
const adminDialogMode = ref<'create' | 'edit'>('create')
const adminSearchKeyword = ref('')
const adminPagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 管理员表单数据
const adminForm = reactive<AdminUserCreate>({
  username: '',
  email: '',
  password: '',
  full_name: '',
  role: 'VIEWER',
  is_active: true,
  is_superuser: false,
  phone: '',
  department: '',
  position: '',
  bio: ''
})

const currentEditingAdmin = ref<AdminUser | null>(null)

// 用户信息
const userInfo = reactive({
  id: 0,
  username: '',
  full_name: '',
  email: '',
  phone: '',
  department: '',
  position: '',
  bio: '',
  avatar: '',
  role: 'user',
  is_active: true,
  two_factor_enabled: false,
  last_login: '',
  created_at: '',
  login_count: 0
})

// 基本信息表单
const basicForm = reactive({
  username: '',
  full_name: '',
  email: '',
  phone: '',
  department: '',
  position: '',
  bio: ''
})

// 密码修改表单
const passwordForm = reactive({
  current_password: '',
  new_password: '',
  confirm_password: ''
})

// 偏好设置表单
const preferencesForm = reactive({
  language: 'zh-CN',
  timezone: 'Asia/Shanghai',
  theme: 'light',
  email_notifications: ['system_updates', 'security_alerts'],
  default_page_size: 20
})

// 登录历史
const loginHistory = ref([])
const loginHistoryPage = ref(1)
const loginHistoryPageSize = ref(10)
const loginHistoryTotal = ref(0)

// 表单验证规则
const basicRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码', trigger: 'blur' }
  ]
}

const passwordRules: FormRules = {
  current_password: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  new_password: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 8, message: '密码长度至少8位', trigger: 'blur' }
  ],
  confirm_password: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.new_password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 计算属性
const canEditUsername = computed(() => {
  return authStore.user?.role === 'admin'
})

const uploadUrl = computed(() => {
  return '/profile/avatar'
})

const uploadHeaders = computed(() => {
  return {
    Authorization: `Bearer ${authStore.accessToken}`
  }
})

// 生命周期
onMounted(() => {
  loadUserProfile()
  loadLoginHistory()
})

// 方法
const loadUserProfile = async () => {
  try {
    const data = await profileApi.getProfile()
    Object.assign(userInfo, data)
    Object.assign(basicForm, {
      username: data.username,
      full_name: data.full_name,
      email: data.email,
      phone: data.phone,
      department: data.department,
      position: data.position,
      bio: data.bio
    })
    Object.assign(preferencesForm, data.preferences || {})
  } catch (error) {
    // 静默处理错误，不显示错误信息
    console.warn('无法加载用户资料，可能未登录或权限不足')
  }
}

const saveBasicInfo = async () => {
  if (!basicFormRef.value) return
  
  try {
    await basicFormRef.value.validate()
    saving.value = true
    
    await profileApi.updateProfile(basicForm)
    Object.assign(userInfo, basicForm)
    
    ElMessage.success('基本信息保存成功')
  } catch (error) {
    ElMessage.error('保存基本信息失败')
  } finally {
    saving.value = false
  }
}

const changePassword = async () => {
  if (!passwordFormRef.value) return
  
  try {
    await passwordFormRef.value.validate()
    changingPassword.value = true
    
    await profileApi.changePassword({
      current_password: passwordForm.current_password,
      new_password: passwordForm.new_password
    })
    
    resetPasswordForm()
    ElMessage.success('密码修改成功')
  } catch (error) {
    ElMessage.error('密码修改失败')
  } finally {
    changingPassword.value = false
  }
}

const savePreferences = async () => {
  try {
    saving.value = true
    await profileApi.updatePreferences(preferencesForm)
    ElMessage.success('偏好设置保存成功')
  } catch (error) {
    ElMessage.error('保存偏好设置失败')
  } finally {
    saving.value = false
  }
}

const loadLoginHistory = async () => {
  try {
    const data = await profileApi.getLoginHistory({
      page: loginHistoryPage.value,
      page_size: loginHistoryPageSize.value
    })
    loginHistory.value = data.items
    loginHistoryTotal.value = data.total
  } catch (error) {
    // 静默处理错误，不显示错误信息
    console.warn('无法加载登录历史，可能未登录或权限不足')
  }
}

const handleAvatarSuccess: UploadProps['onSuccess'] = (response) => {
  userInfo.avatar = response.avatar_url
  ElMessage.success('头像上传成功')
}

const beforeAvatarUpload: UploadProps['beforeUpload'] = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('头像图片只能是 JPG/PNG 格式!')
  }
  if (!isLt2M) {
    ElMessage.error('头像图片大小不能超过 2MB!')
  }
  return isJPG && isLt2M
}

const checkPasswordStrength = (password: string) => {
  if (!password) {
    passwordStrength.value = null
    return
  }
  
  let score = 0
  let level = 'weak'
  let text = '弱'
  
  if (password.length >= 8) score += 25
  if (/[a-z]/.test(password)) score += 25
  if (/[A-Z]/.test(password)) score += 25
  if (/\d/.test(password)) score += 25
  
  if (score >= 75) {
    level = 'strong'
    text = '强'
  } else if (score >= 50) {
    level = 'medium'
    text = '中等'
  }
  
  passwordStrength.value = { score, level, text }
}

const enableTwoFactor = async () => {
  try {
    await profileApi.enableTwoFactor()
    userInfo.two_factor_enabled = true
    ElMessage.success('双因素认证已启用')
  } catch (error) {
    ElMessage.error('启用双因素认证失败')
  }
}

const disableTwoFactor = async () => {
  try {
    await ElMessageBox.confirm('确定要禁用双因素认证吗？', '确认操作', {
      type: 'warning'
    })
    
    await profileApi.disableTwoFactor()
    userInfo.two_factor_enabled = false
    ElMessage.success('双因素认证已禁用')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('禁用双因素认证失败')
    }
  }
}

const resetBasicForm = () => {
  basicFormRef.value?.resetFields()
}

const resetPasswordForm = () => {
  passwordFormRef.value?.resetFields()
  passwordStrength.value = null
}

const resetPreferencesForm = () => {
  preferencesFormRef.value?.resetFields()
}

const getRoleText = (role: string) => {
  const roleMap: Record<string, string> = {
    admin: '管理员',
    user: '普通用户',
    moderator: '版主'
  }
  return roleMap[role] || role
}

const formatTime = (time: string) => {
  return time ? new Date(time).toLocaleString('zh-CN') : '从未'
}

const formatDate = (date: string) => {
  return date ? new Date(date).toLocaleDateString('zh-CN') : '未知'
}
</script>

<style lang="scss" scoped>
.profile-page {
  .page-header {
    margin-bottom: 20px;
    
    h1 {
      margin: 0 0 4px 0;
      font-size: 24px;
      color: #303133;
    }
    
    p {
      margin: 0;
      color: #909399;
    }
  }
  
  .profile-container {
    display: flex;
    gap: 20px;
    
    .profile-sidebar {
      width: 300px;
      flex-shrink: 0;
      
      .profile-card {
        .avatar-section {
          text-align: center;
          padding-bottom: 20px;
          border-bottom: 1px solid var(--el-border-color-lighter);
          
          .avatar-uploader {
            .avatar {
              width: 100px;
              height: 100px;
              border-radius: 50%;
              object-fit: cover;
            }
            
            .avatar-uploader-icon {
              font-size: 28px;
              color: #8c939d;
              width: 100px;
              height: 100px;
              line-height: 100px;
              text-align: center;
              border: 1px dashed var(--el-border-color);
              border-radius: 50%;
            }
          }
          
          .avatar-info {
            margin-top: 16px;
            
            h3 {
              margin: 0 0 8px 0;
              font-size: 18px;
              color: #303133;
            }
            
            .role-tag {
              color: var(--el-color-primary);
              font-size: 14px;
              margin: 0 0 4px 0;
            }
            
            .last-login {
              color: #909399;
              font-size: 12px;
              margin: 0;
            }
          }
        }
        
        .stats-section {
          padding-top: 20px;
          
          .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            
            .stat-label {
              color: #606266;
              font-size: 14px;
            }
            
            .stat-value {
              color: #303133;
              font-weight: 500;
            }
          }
        }
      }
    }
    
    .profile-content {
      flex: 1;
      
      .profile-form {
        max-width: 600px;
        
        .form-tip {
          font-size: 12px;
          color: #909399;
          margin-top: 4px;
        }
        
        .password-strength {
          margin-top: 8px;
          
          .strength-bar {
            width: 100%;
            height: 6px;
            background: var(--el-border-color-lighter);
            border-radius: 3px;
            overflow: hidden;
            
            .strength-fill {
              height: 100%;
              transition: all 0.3s ease;
              
              &.weak {
                background: var(--el-color-danger);
              }
              
              &.medium {
                background: var(--el-color-warning);
              }
              
              &.strong {
                background: var(--el-color-success);
              }
            }
          }
          
          .strength-text {
            margin-top: 4px;
            font-size: 12px;
            color: var(--el-text-color-regular);
          }
        }
      }
      
      .security-section {
        margin-bottom: 32px;
        
        h4 {
          margin: 0 0 16px 0;
          font-size: 16px;
          color: #303133;
        }
        
        .two-factor-auth {
          .auth-status {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 12px;
          }
          
          .auth-actions {
            margin-bottom: 8px;
          }
          
          .auth-description {
            font-size: 12px;
            color: #909399;
          }
        }
      }
      
      .table-pagination {
        margin-top: 16px;
        text-align: right;
      }
    }
  }
}
</style>
