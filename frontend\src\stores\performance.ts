/**
 * 性能监控数据状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed, reactive } from 'vue'
import performanceService from '@/services/performanceService'
import type {
  PerformanceMetrics,
  SystemHealth,
  FrontendMetrics,
  HistoricalData,
  QuickStats,
  PerformanceAlert,
  PerformanceConfig,
  MetricCard
} from '@/types/performance'

export const usePerformanceStore = defineStore('performance', () => {
  // 状态数据
  const loading = ref(false)
  const error = ref<string | null>(null)
  const lastUpdate = ref<Date | null>(null)

  // 性能指标数据
  const metrics = ref<PerformanceMetrics | null>(null)
  const systemHealth = ref<SystemHealth | null>(null)
  const frontendMetrics = ref<FrontendMetrics | null>(null)
  const quickStats = ref<QuickStats | null>(null)
  const alerts = ref<PerformanceAlert[]>([])
  const config = ref<PerformanceConfig | null>(null)

  // 历史数据
  const historicalData = reactive<Map<string, HistoricalData>>(new Map())

  // 实时监控状态
  const realTimeEnabled = ref(true)
  const updateInterval = ref(30000) // 30秒
  let unsubscribe: (() => void) | null = null

  // 计算属性 - 性能指标卡片
  const metricCards = computed((): MetricCard[] => {
    if (!metrics.value) return []

    return [
      {
        label: 'CPU使用率',
        value: metrics.value.cpu.usage,
        unit: '%',
        change: metrics.value.cpu.change || 0,
        color: '#3b82f6',
        icon: 'Monitor',
        status: getMetricStatus(metrics.value.cpu.usage),
        trend: getTrend(metrics.value.cpu.change || 0)
      },
      {
        label: '内存使用',
        value: metrics.value.memory.usage,
        unit: '%',
        change: metrics.value.memory.change || 0,
        color: '#10b981',
        icon: 'DataBoard',
        status: getMetricStatus(metrics.value.memory.usage),
        trend: getTrend(metrics.value.memory.change || 0)
      },
      {
        label: '磁盘使用',
        value: metrics.value.disk.usage,
        unit: '%',
        change: metrics.value.disk.change || 0,
        color: '#f59e0b',
        icon: 'TrendCharts',
        status: getMetricStatus(metrics.value.disk.usage),
        trend: getTrend(metrics.value.disk.change || 0)
      },
      {
        label: '网络延迟',
        value: metrics.value.network.latency,
        unit: 'ms',
        change: metrics.value.network.change || 0,
        color: '#8b5cf6',
        icon: 'Connection',
        status: getLatencyStatus(metrics.value.network.latency),
        trend: getTrend(metrics.value.network.change || 0)
      }
    ]
  })

  // 计算属性 - 系统整体状态
  const overallStatus = computed(() => {
    if (!systemHealth.value) return 'unknown'
    return systemHealth.value.overall
  })

  // 计算属性 - 活跃告警数量
  const activeAlertsCount = computed(() => {
    return alerts.value.filter(alert => !alert.resolved).length
  })

  // 计算属性 - 是否有严重告警
  const hasCriticalAlerts = computed(() => {
    return alerts.value.some(alert => alert.type === 'error' && !alert.resolved)
  })

  // Actions

  /**
   * 初始化性能监控
   */
  async function initialize() {
    try {
      loading.value = true
      error.value = null

      // 并行获取所有初始数据
      await Promise.all([
        fetchMetrics(),
        fetchSystemHealth(),
        fetchQuickStats(),
        fetchAlerts(),
        fetchConfig()
      ])

      // 获取前端性能数据（同步）
      frontendMetrics.value = performanceService.getFrontendMetrics()

      // 启动实时监控
      if (realTimeEnabled.value) {
        startRealTimeMonitoring()
      }

      lastUpdate.value = new Date()
    } catch (err) {
      error.value = err instanceof Error ? err.message : '初始化失败'
      console.error('Performance store initialization failed:', err)
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取实时性能指标
   */
  async function fetchMetrics() {
    try {
      metrics.value = await performanceService.getRealTimeMetrics()
      lastUpdate.value = new Date()
    } catch (err) {
      error.value = '获取性能指标失败'
      throw err
    }
  }

  /**
   * 获取系统健康状态
   */
  async function fetchSystemHealth() {
    try {
      systemHealth.value = await performanceService.getSystemHealth()
    } catch (err) {
      error.value = '获取系统健康状态失败'
      throw err
    }
  }

  /**
   * 获取快速统计
   */
  async function fetchQuickStats() {
    try {
      quickStats.value = await performanceService.getQuickStats()
    } catch (err) {
      error.value = '获取快速统计失败'
      throw err
    }
  }

  /**
   * 获取性能告警
   */
  async function fetchAlerts() {
    try {
      alerts.value = await performanceService.getAlerts()
    } catch (err) {
      error.value = '获取性能告警失败'
      throw err
    }
  }

  /**
   * 获取性能配置
   */
  async function fetchConfig() {
    try {
      config.value = await performanceService.getConfig()
    } catch (err) {
      error.value = '获取性能配置失败'
      throw err
    }
  }

  /**
   * 获取历史数据
   */
  async function fetchHistoricalData(timeRange: string) {
    try {
      const data = await performanceService.getHistoricalData(timeRange)
      historicalData.set(timeRange, data)
      return data
    } catch (err) {
      error.value = '获取历史数据失败'
      throw err
    }
  }

  /**
   * 更新性能配置
   */
  async function updateConfig(newConfig: Partial<PerformanceConfig>) {
    try {
      await performanceService.updateConfig(newConfig)
      
      // 重新获取配置
      await fetchConfig()
      
      // 如果更新了刷新间隔，重启实时监控
      if (newConfig.refreshInterval && realTimeEnabled.value) {
        updateInterval.value = newConfig.refreshInterval * 1000
        restartRealTimeMonitoring()
      }
    } catch (err) {
      error.value = '更新配置失败'
      throw err
    }
  }

  /**
   * 启动实时监控
   */
  function startRealTimeMonitoring() {
    if (unsubscribe) {
      unsubscribe()
    }

    unsubscribe = performanceService.subscribeToUpdates(
      (data) => {
        metrics.value = data
        lastUpdate.value = new Date()
        
        // 同时更新前端性能数据
        frontendMetrics.value = performanceService.getFrontendMetrics()
      },
      {
        interval: updateInterval.value,
        immediate: false,
        onError: (err) => {
          error.value = '实时监控更新失败'
          console.error('Real-time monitoring error:', err)
        }
      }
    )

    realTimeEnabled.value = true
  }

  /**
   * 停止实时监控
   */
  function stopRealTimeMonitoring() {
    if (unsubscribe) {
      unsubscribe()
      unsubscribe = null
    }
    realTimeEnabled.value = false
  }

  /**
   * 重启实时监控
   */
  function restartRealTimeMonitoring() {
    stopRealTimeMonitoring()
    if (realTimeEnabled.value) {
      startRealTimeMonitoring()
    }
  }

  /**
   * 切换实时监控状态
   */
  function toggleRealTimeMonitoring() {
    if (realTimeEnabled.value) {
      stopRealTimeMonitoring()
    } else {
      startRealTimeMonitoring()
    }
  }

  /**
   * 刷新所有数据
   */
  async function refreshAll() {
    try {
      loading.value = true
      error.value = null

      await Promise.all([
        fetchMetrics(),
        fetchSystemHealth(),
        fetchQuickStats(),
        fetchAlerts()
      ])

      frontendMetrics.value = performanceService.getFrontendMetrics()
      lastUpdate.value = new Date()
    } catch (err) {
      error.value = '刷新数据失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 清除缓存
   */
  function clearCache() {
    performanceService.clearCache()
  }

  /**
   * 添加告警
   */
  function addAlert(alert: Omit<PerformanceAlert, 'id' | 'timestamp'>) {
    const newAlert: PerformanceAlert = {
      ...alert,
      id: Math.random().toString(36).substr(2, 9),
      timestamp: new Date()
    }
    alerts.value.unshift(newAlert)
  }

  /**
   * 解决告警
   */
  function resolveAlert(alertId: string) {
    const alert = alerts.value.find(a => a.id === alertId)
    if (alert) {
      alert.resolved = true
    }
  }

  /**
   * 删除告警
   */
  function removeAlert(alertId: string) {
    const index = alerts.value.findIndex(a => a.id === alertId)
    if (index > -1) {
      alerts.value.splice(index, 1)
    }
  }

  /**
   * 清除所有告警
   */
  function clearAllAlerts() {
    alerts.value = []
  }

  // 工具函数
  function getMetricStatus(value: number): 'normal' | 'warning' | 'error' | 'excellent' {
    if (value >= 90) return 'error'
    if (value >= 75) return 'warning'
    if (value <= 30) return 'excellent'
    return 'normal'
  }

  function getLatencyStatus(latency: number): 'normal' | 'warning' | 'error' | 'excellent' {
    if (latency >= 500) return 'error'
    if (latency >= 200) return 'warning'
    if (latency <= 50) return 'excellent'
    return 'normal'
  }

  function getTrend(change: number): 'up' | 'down' | 'stable' {
    if (Math.abs(change) < 1) return 'stable'
    return change > 0 ? 'up' : 'down'
  }

  // 清理函数
  function $dispose() {
    stopRealTimeMonitoring()
    performanceService.destroy()
  }

  return {
    // 状态
    loading,
    error,
    lastUpdate,
    metrics,
    systemHealth,
    frontendMetrics,
    quickStats,
    alerts,
    config,
    historicalData,
    realTimeEnabled,
    updateInterval,

    // 计算属性
    metricCards,
    overallStatus,
    activeAlertsCount,
    hasCriticalAlerts,

    // 方法
    initialize,
    fetchMetrics,
    fetchSystemHealth,
    fetchQuickStats,
    fetchAlerts,
    fetchConfig,
    fetchHistoricalData,
    updateConfig,
    startRealTimeMonitoring,
    stopRealTimeMonitoring,
    restartRealTimeMonitoring,
    toggleRealTimeMonitoring,
    refreshAll,
    clearCache,
    addAlert,
    resolveAlert,
    removeAlert,
    clearAllAlerts,
    $dispose
  }
})
