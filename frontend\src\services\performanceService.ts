/**
 * 统一的性能数据服务
 * 负责管理所有性能监控相关的数据获取、缓存和实时更新
 */

import { ref, reactive } from 'vue'
import type {
  PerformanceMetrics,
  SystemHealth,
  FrontendMetrics,
  HistoricalData,
  QuickStats,
  PerformanceAlert,
  PerformanceConfig,
  DataUpdateCallback,
  SubscriptionConfig,
  ServiceConfig,
  ServiceError,
  CacheConfig
} from '@/types/performance'
import { cache } from '@/utils/cache'

class PerformanceDataService {
  private config: ServiceConfig
  private cacheConfig: CacheConfig
  private updateCallbacks: Map<string, DataUpdateCallback<any>> = new Map()
  private updateTimer: number | null = null
  private isDestroyed = false

  constructor(config?: Partial<ServiceConfig>) {
    this.config = {
      baseURL: '/api',
      timeout: 10000,
      retryAttempts: 3,
      cacheTimeout: 30000, // 30秒缓存
      enableRealTime: true,
      ...config
    }

    this.cacheConfig = {
      ttl: 30, // 30秒
      maxSize: 100,
      enablePersist: false
    }
  }

  /**
   * 获取实时性能指标
   */
  async getRealTimeMetrics(): Promise<PerformanceMetrics> {
    const cacheKey = 'performance-metrics'
    
    // 尝试从缓存获取
    const cached = cache.get(cacheKey)
    if (cached) {
      return cached
    }

    try {
      // 模拟API调用 - 实际项目中应该调用真实API
      const data = await this.mockApiCall<PerformanceMetrics>('/system/performance', {
        cpu: {
          usage: Math.floor(Math.random() * 100),
          cores: 8,
          frequency: 3.2,
          change: (Math.random() - 0.5) * 20
        },
        memory: {
          total: 16,
          used: Math.floor(Math.random() * 16),
          available: 0,
          usage: 0,
          change: (Math.random() - 0.5) * 10
        },
        disk: {
          total: 512,
          used: Math.floor(Math.random() * 512),
          free: 0,
          usage: 0,
          ioRead: Math.floor(Math.random() * 100),
          ioWrite: Math.floor(Math.random() * 100),
          change: (Math.random() - 0.5) * 15
        },
        network: {
          latency: Math.floor(Math.random() * 100) + 10,
          uploadSpeed: Math.floor(Math.random() * 100),
          downloadSpeed: Math.floor(Math.random() * 100),
          packetsLost: Math.random() * 2,
          change: (Math.random() - 0.5) * 30
        },
        timestamp: new Date().toISOString()
      })

      // 计算衍生数据
      data.memory.available = data.memory.total - data.memory.used
      data.memory.usage = Math.round((data.memory.used / data.memory.total) * 100)
      data.disk.free = data.disk.total - data.disk.used
      data.disk.usage = Math.round((data.disk.used / data.disk.total) * 100)

      // 缓存数据
      cache.set(cacheKey, data, this.cacheConfig.ttl)
      
      return data
    } catch (error) {
      throw this.handleError('METRICS_FETCH_ERROR', '获取性能指标失败', error)
    }
  }

  /**
   * 获取历史性能数据
   */
  async getHistoricalData(timeRange: string): Promise<HistoricalData> {
    const cacheKey = `historical-data-${timeRange}`
    
    const cached = cache.get(cacheKey)
    if (cached) {
      return cached
    }

    try {
      const dataPoints = this.generateHistoricalData(timeRange)
      const data: HistoricalData = {
        timeRange,
        dataPoints
      }

      cache.set(cacheKey, data, 300) // 5分钟缓存
      return data
    } catch (error) {
      throw this.handleError('HISTORICAL_DATA_ERROR', '获取历史数据失败', error)
    }
  }

  /**
   * 获取系统健康状态
   */
  async getSystemHealth(): Promise<SystemHealth> {
    const cacheKey = 'system-health'
    
    const cached = cache.get(cacheKey)
    if (cached) {
      return cached
    }

    try {
      const data: SystemHealth = {
        overall: 'healthy',
        components: [
          {
            name: '数据库',
            status: 'healthy',
            statusText: '正常',
            details: '连接正常，响应时间 < 50ms',
            lastCheck: new Date().toISOString(),
            color: '#10b981',
            icon: 'DataBoard'
          },
          {
            name: 'Web服务',
            status: 'healthy',
            statusText: '正常',
            details: '服务运行正常，负载均衡',
            lastCheck: new Date().toISOString(),
            color: '#10b981',
            icon: 'Monitor'
          },
          {
            name: '缓存服务',
            status: 'warning',
            statusText: '警告',
            details: '缓存使用率较高: 85%',
            lastCheck: new Date().toISOString(),
            color: '#f59e0b',
            icon: 'TrendCharts'
          }
        ],
        uptime: Math.floor(Math.random() * 86400 * 30), // 30天内随机
        version: '1.0.0',
        lastCheck: new Date()
      }

      // 根据组件状态确定整体状态
      const hasError = data.components.some(c => c.status === 'error')
      const hasWarning = data.components.some(c => c.status === 'warning')
      
      if (hasError) {
        data.overall = 'error'
      } else if (hasWarning) {
        data.overall = 'warning'
      }

      cache.set(cacheKey, data, 60) // 1分钟缓存
      return data
    } catch (error) {
      throw this.handleError('HEALTH_CHECK_ERROR', '获取系统健康状态失败', error)
    }
  }

  /**
   * 获取前端性能数据
   */
  getFrontendMetrics(): FrontendMetrics {
    try {
      const data: FrontendMetrics = {
        pageLoad: {
          domContentLoaded: 0,
          loadComplete: 0,
          firstPaint: 0,
          firstContentfulPaint: 0
        },
        memory: {
          usedJSHeapSize: 0,
          totalJSHeapSize: 0,
          jsHeapSizeLimit: 0
        },
        cache: {
          hitRate: 0,
          totalItems: 0,
          activeItems: 0,
          expiredItems: 0
        },
        api: {
          averageResponseTime: 0,
          requestsPerMinute: 0,
          errorRate: 0,
          recentRequests: []
        }
      }

      // 获取页面加载性能
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      if (navigation) {
        data.pageLoad.domContentLoaded = Math.round(navigation.domContentLoadedEventEnd - navigation.fetchStart)
        data.pageLoad.loadComplete = Math.round(navigation.loadEventEnd - navigation.fetchStart)
      }

      // 获取绘制性能
      const paintEntries = performance.getEntriesByType('paint')
      paintEntries.forEach(entry => {
        if (entry.name === 'first-paint') {
          data.pageLoad.firstPaint = Math.round(entry.startTime)
        } else if (entry.name === 'first-contentful-paint') {
          data.pageLoad.firstContentfulPaint = Math.round(entry.startTime)
        }
      })

      // 获取内存使用情况
      if ('memory' in performance) {
        const memory = (performance as any).memory
        data.memory.usedJSHeapSize = Math.round(memory.usedJSHeapSize / 1024 / 1024)
        data.memory.totalJSHeapSize = Math.round(memory.totalJSHeapSize / 1024 / 1024)
        data.memory.jsHeapSizeLimit = Math.round(memory.jsHeapSizeLimit / 1024 / 1024)
      }

      // 获取缓存统计
      const cacheStats = cache.getStats()
      data.cache = {
        hitRate: Math.round(cacheStats.hit_rate * 100) || 0,
        totalItems: cacheStats.total,
        activeItems: cacheStats.total - cacheStats.expired,
        expiredItems: cacheStats.expired
      }

      // 获取API请求统计
      const apiRequests = this.getApiRequestStats()
      data.api = apiRequests

      return data
    } catch (error) {
      console.warn('获取前端性能数据失败:', error)
      // 返回默认数据而不是抛出错误
      return {
        pageLoad: { domContentLoaded: 0, loadComplete: 0, firstPaint: 0, firstContentfulPaint: 0 },
        memory: { usedJSHeapSize: 0, totalJSHeapSize: 0, jsHeapSizeLimit: 0 },
        cache: { hitRate: 0, totalItems: 0, activeItems: 0, expiredItems: 0 },
        api: { averageResponseTime: 0, requestsPerMinute: 0, errorRate: 0, recentRequests: [] }
      }
    }
  }

  /**
   * 获取快速统计数据
   */
  async getQuickStats(): Promise<QuickStats> {
    const cacheKey = 'quick-stats'

    const cached = cache.get(cacheKey)
    if (cached) {
      return cached
    }

    try {
      const data: QuickStats = {
        onlineUsers: Math.floor(Math.random() * 2000) + 500,
        apiRequestsPerMinute: Math.floor(Math.random() * 5000) + 1000,
        errorRate: Math.random() * 2,
        systemLoad: Math.random() * 100,
        activeConnections: Math.floor(Math.random() * 1000) + 100,
        cacheHitRate: Math.random() * 100
      }

      cache.set(cacheKey, data, 30) // 30秒缓存
      return data
    } catch (error) {
      throw this.handleError('QUICK_STATS_ERROR', '获取快速统计失败', error)
    }
  }

  /**
   * 获取性能告警
   */
  async getAlerts(): Promise<PerformanceAlert[]> {
    const cacheKey = 'performance-alerts'

    const cached = cache.get(cacheKey)
    if (cached) {
      return cached
    }

    try {
      const alerts: PerformanceAlert[] = [
        {
          id: '1',
          title: 'CPU使用率过高',
          description: 'CPU使用率已达到85%，建议检查系统负载',
          type: 'warning',
          timestamp: new Date(),
          metric: 'CPU: 85%',
          threshold: 80,
          currentValue: 85
        },
        {
          id: '2',
          title: '内存使用率警告',
          description: '内存使用率超过阈值，可能影响系统性能',
          type: 'error',
          timestamp: new Date(Date.now() - 300000),
          metric: 'Memory: 92%',
          threshold: 85,
          currentValue: 92
        }
      ]

      cache.set(cacheKey, alerts, 60) // 1分钟缓存
      return alerts
    } catch (error) {
      throw this.handleError('ALERTS_FETCH_ERROR', '获取性能告警失败', error)
    }
  }

  /**
   * 获取性能配置
   */
  async getConfig(): Promise<PerformanceConfig> {
    const cacheKey = 'performance-config'

    const cached = cache.get(cacheKey)
    if (cached) {
      return cached
    }

    try {
      const config: PerformanceConfig = {
        enableMonitoring: true,
        refreshInterval: 30,
        monitoringMetrics: ['cpu', 'memory', 'disk', 'network', 'api'],
        alertThresholds: {
          cpu: 80,
          memory: 85,
          disk: 90,
          responseTime: 1000
        },
        alertMethods: ['email', 'browser'],
        dataRetentionDays: 30,
        samplingInterval: 5
      }

      cache.set(cacheKey, config, 300) // 5分钟缓存
      return config
    } catch (error) {
      throw this.handleError('CONFIG_FETCH_ERROR', '获取性能配置失败', error)
    }
  }

  /**
   * 更新性能配置
   */
  async updateConfig(config: Partial<PerformanceConfig>): Promise<void> {
    try {
      // 模拟API调用
      await this.mockApiCall('/system/performance/config', config)

      // 清除配置缓存
      cache.remove('performance-config')

      console.log('性能配置已更新:', config)
    } catch (error) {
      throw this.handleError('CONFIG_UPDATE_ERROR', '更新性能配置失败', error)
    }
  }

  /**
   * 订阅实时更新
   */
  subscribeToUpdates(
    callback: DataUpdateCallback<PerformanceMetrics>,
    config: SubscriptionConfig = {}
  ): () => void {
    if (this.isDestroyed) {
      throw new Error('Service has been destroyed')
    }

    const callbackId = Math.random().toString(36).substr(2, 9)
    this.updateCallbacks.set(callbackId, callback)

    // 如果是第一个订阅者，启动定时更新
    if (this.updateCallbacks.size === 1 && this.config.enableRealTime) {
      this.startRealTimeUpdates(config.interval || 30000) // 默认30秒
    }

    // 如果需要立即执行
    if (config.immediate !== false) {
      this.getRealTimeMetrics()
        .then(callback)
        .catch(config.onError || console.error)
    }

    // 返回取消订阅的函数
    return () => {
      this.updateCallbacks.delete(callbackId)
      
      // 如果没有订阅者了，停止定时更新
      if (this.updateCallbacks.size === 0) {
        this.stopRealTimeUpdates()
      }
    }
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    cache.clear()
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    this.isDestroyed = true
    this.stopRealTimeUpdates()
    this.updateCallbacks.clear()
    this.clearCache()
  }

  // 私有方法

  private async mockApiCall<T>(endpoint: string, mockData: T): Promise<T> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, Math.random() * 500 + 100))
    
    // 模拟偶尔的网络错误
    if (Math.random() < 0.05) { // 5% 错误率
      throw new Error('Network error')
    }
    
    return mockData
  }

  private generateHistoricalData(timeRange: string) {
    const now = new Date()
    const dataPoints = []
    let interval: number
    let count: number

    switch (timeRange) {
      case '1h':
        interval = 5 * 60 * 1000 // 5分钟
        count = 12
        break
      case '6h':
        interval = 30 * 60 * 1000 // 30分钟
        count = 12
        break
      case '24h':
        interval = 2 * 60 * 60 * 1000 // 2小时
        count = 12
        break
      default:
        interval = 60 * 60 * 1000 // 1小时
        count = 24
    }

    for (let i = count - 1; i >= 0; i--) {
      const timestamp = new Date(now.getTime() - i * interval)
      dataPoints.push({
        timestamp: timestamp.toISOString(),
        cpu: Math.floor(Math.random() * 100),
        memory: Math.floor(Math.random() * 100),
        disk: Math.floor(Math.random() * 100),
        network: Math.floor(Math.random() * 100)
      })
    }

    return dataPoints
  }

  private getApiRequestStats() {
    // 获取最近的网络请求
    const entries = performance.getEntriesByType('resource') as PerformanceResourceTiming[]
    const apiRequests = entries
      .filter(entry => entry.name.includes('/api/'))
      .slice(-10) // 最近10个请求
      .map((entry, index) => ({
        id: `${entry.startTime}-${index}`,
        method: 'GET', // 简化处理
        url: entry.name.replace(window.location.origin, ''),
        duration: Math.round(entry.responseEnd - entry.requestStart),
        status: 200, // 简化处理
        timestamp: entry.startTime
      }))

    const averageResponseTime = apiRequests.length > 0
      ? Math.round(apiRequests.reduce((sum, req) => sum + req.duration, 0) / apiRequests.length)
      : 0

    return {
      averageResponseTime,
      requestsPerMinute: Math.floor(Math.random() * 100) + 50,
      errorRate: Math.random() * 5,
      recentRequests: apiRequests
    }
  }

  private startRealTimeUpdates(interval: number): void {
    if (this.updateTimer) {
      clearInterval(this.updateTimer)
    }

    this.updateTimer = window.setInterval(async () => {
      if (this.isDestroyed || this.updateCallbacks.size === 0) {
        return
      }

      try {
        const data = await this.getRealTimeMetrics()
        this.updateCallbacks.forEach(callback => {
          try {
            callback(data)
          } catch (error) {
            console.error('Update callback error:', error)
          }
        })
      } catch (error) {
        console.error('Real-time update error:', error)
      }
    }, interval)
  }

  private stopRealTimeUpdates(): void {
    if (this.updateTimer) {
      clearInterval(this.updateTimer)
      this.updateTimer = null
    }
  }

  private handleError(code: string, message: string, originalError: any): ServiceError {
    const error: ServiceError = {
      code,
      message,
      details: originalError,
      timestamp: new Date()
    }
    
    console.error(`[PerformanceService] ${message}:`, originalError)
    return error
  }
}

// 创建单例实例
export const performanceService = new PerformanceDataService()

// 导出类型和服务
export default performanceService
export type { PerformanceDataService }
