/**
 * 个人资料相关API
 */
import request from './request'

export interface UserProfile {
  id: number
  username: string
  full_name: string
  email: string
  phone?: string
  department?: string
  position?: string
  bio?: string
  avatar?: string
  role: string
  is_active: boolean
  two_factor_enabled: boolean
  last_login?: string
  created_at: string
  login_count: number
  preferences?: UserPreferences
}

export interface UserPreferences {
  language: string
  timezone: string
  theme: string
  email_notifications: string[]
  default_page_size: number
}

export interface PasswordChangeRequest {
  current_password: string
  new_password: string
}

export interface LoginHistoryItem {
  id: number
  login_time: string
  ip_address: string
  user_agent: string
  location?: string
  status: 'success' | 'failed'
}

export interface LoginHistoryResponse {
  items: LoginHistoryItem[]
  total: number
  page: number
  page_size: number
}

export const profileApi = {
  /**
   * 获取用户资料
   */
  async getProfile(): Promise<UserProfile> {
    return request.get('/profile')
  },

  /**
   * 更新用户基本信息
   */
  async updateProfile(data: Partial<UserProfile>): Promise<UserProfile> {
    return request.put('/profile', data)
  },

  /**
   * 修改密码
   */
  async changePassword(data: PasswordChangeRequest): Promise<void> {
    return request.post('/profile/change-password', data)
  },

  /**
   * 更新用户偏好设置
   */
  async updatePreferences(preferences: UserPreferences): Promise<void> {
    return request.put('/profile/preferences', preferences)
  },

  /**
   * 上传头像
   */
  async uploadAvatar(file: File): Promise<{ avatar_url: string }> {
    const formData = new FormData()
    formData.append('avatar', file)
    
    return request.post('/profile/avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 获取登录历史
   */
  async getLoginHistory(params: {
    page?: number
    page_size?: number
  }): Promise<LoginHistoryResponse> {
    return request.get('/profile/login-history', { params })
  },

  /**
   * 启用双因素认证
   */
  async enableTwoFactor(): Promise<{
    qr_code: string
    secret: string
    backup_codes: string[]
  }> {
    return request.post('/profile/enable-2fa')
  },

  /**
   * 确认双因素认证
   */
  async confirmTwoFactor(code: string): Promise<void> {
    return request.post('/profile/confirm-2fa', { code })
  },

  /**
   * 禁用双因素认证
   */
  async disableTwoFactor(password: string): Promise<void> {
    return request.post('/profile/disable-2fa', { password })
  },

  /**
   * 生成备用代码
   */
  async generateBackupCodes(): Promise<{ backup_codes: string[] }> {
    return request.post('/profile/generate-backup-codes')
  },

  /**
   * 验证当前密码
   */
  async verifyPassword(password: string): Promise<{ valid: boolean }> {
    return request.post('/profile/verify-password', { password })
  },

  /**
   * 获取账户安全状态
   */
  async getSecurityStatus(): Promise<{
    password_strength: string
    last_password_change: string
    failed_login_attempts: number
    active_sessions: number
    two_factor_enabled: boolean
  }> {
    return request.get('/profile/security-status')
  },

  /**
   * 注销所有其他会话
   */
  async logoutOtherSessions(): Promise<{ logged_out_sessions: number }> {
    return request.post('/profile/logout-other-sessions')
  },

  /**
   * 获取活跃会话列表
   */
  async getActiveSessions(): Promise<Array<{
    id: string
    ip_address: string
    user_agent: string
    location?: string
    created_at: string
    last_activity: string
    is_current: boolean
  }>> {
    return request.get('/profile/active-sessions')
  },

  /**
   * 注销指定会话
   */
  async logoutSession(sessionId: string): Promise<void> {
    return request.delete(`/profile/sessions/${sessionId}`)
  },

  /**
   * 导出个人数据
   */
  async exportPersonalData(): Promise<Blob> {
    return request.get('/profile/export-data', {
      responseType: 'blob'
    })
  },

  /**
   * 删除账户
   */
  async deleteAccount(data: {
    password: string
    confirmation: string
  }): Promise<void> {
    return request.post('/profile/delete-account', data)
  }
}

export default profileApi
