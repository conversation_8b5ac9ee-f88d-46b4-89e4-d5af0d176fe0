"""
WisCude 后台管理系统 - 主应用入口
"""
from fastapi import FastAPI, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
from contextlib import asynccontextmanager
import logging
import time
import uvicorn

# 导入配置和数据库
try:
    from app.core.config import settings, ensure_directories
    from app.core.database import create_tables, db_manager
    # 导入API路由
    from app.api import auth, users, sync, system, profile
    from app.api import settings as settings_api
    from app.api.v1 import community
    # 尝试导入新模块路由（如果已创建）
    try:
        from app.api.v1 import advertising, question_bank, english_practice, psychology, courses, app_updates
    except ImportError:
        pass
    FULL_IMPORTS = True
except ImportError as e:
    print(f"警告: 无法导入完整模块 ({e})，使用简化配置")
    FULL_IMPORTS = False

    # 简化配置
    class SimpleSettings:
        LOG_LEVEL = "INFO"
        LOG_FILE = "logs/wiscude-admin.log"
        APP_NAME = "WisCude 后台管理系统"
        APP_VERSION = "1.0.0"
        DEBUG = True
        HOST = "0.0.0.0"
        PORT = 8000
        CORS_ORIGINS = [
            "http://localhost:3000",
            "http://localhost:5173",
            "http://localhost:5175",
        ]

    settings = SimpleSettings()

    def ensure_directories():
        os.makedirs("logs", exist_ok=True)
        os.makedirs("uploads", exist_ok=True)

# 确保日志目录存在
import os
log_dir = os.path.join(os.path.dirname(__file__), "logs")
os.makedirs(log_dir, exist_ok=True)

# 配置日志
try:
    log_level = getattr(logging, settings.LOG_LEVEL)
    log_file = settings.LOG_FILE
except:
    log_level = logging.INFO
    log_file = os.path.join(log_dir, "wiscude-admin.log")

logging.basicConfig(
    level=log_level,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """优化的应用生命周期管理"""
    startup_start_time = time.time()

    try:
        # 启动时执行
        logger.info("=" * 60)
        logger.info("启动 WisCude 后台管理系统...")
        logger.info("=" * 60)

        # 步骤1: 确保必要目录存在
        logger.info("步骤1: 创建必要目录...")
        ensure_directories()
        logger.info("✓ 目录创建完成")

        # 步骤2: 初始化数据库连接
        logger.info("步骤2: 初始化数据库连接...")
        if FULL_IMPORTS:
            try:
                from app.core.database import enhanced_db_manager
                success = enhanced_db_manager.init_database()
                if success:
                    logger.info("✓ 数据库连接初始化成功")

                    # 创建数据库表
                    enhanced_db_manager.create_tables()
                    logger.info("✓ 数据库表创建完成")
                else:
                    logger.warning("⚠ 数据库初始化失败，将使用简化模式")
            except Exception as e:
                logger.error(f"✗ 数据库初始化失败: {e}")
                logger.warning("⚠ 将使用简化模式运行")
        else:
            logger.info("✓ 简化模式 - 跳过数据库初始化")



        # 步骤4: 启动完成
        startup_time = round(time.time() - startup_start_time, 2)
        logger.info("=" * 60)
        logger.info(f"✓ 应用启动完成! 耗时: {startup_time}秒")
        logger.info(f"✓ 后端服务: http://{settings.HOST}:{settings.PORT}")
        logger.info(f"✓ API文档: http://{settings.HOST}:{settings.PORT}/docs")
        logger.info(f"✓ 健康检查: http://{settings.HOST}:{settings.PORT}/api/health")
        logger.info("=" * 60)

        yield

        # 关闭时执行
        logger.info("正在关闭 WisCude 后台管理系统...")
        logger.info("✓ 应用已安全关闭")

    except Exception as e:
        logger.error(f"✗ 应用启动失败: {e}")
        raise

# 创建FastAPI应用
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    description="WisCude Android应用后台管理系统",
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    lifespan=lifespan
)

# 添加中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["*"] if settings.DEBUG else ["localhost", "127.0.0.1"]
)

# 请求处理时间中间件
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    """添加请求处理时间头"""
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response

# 异常处理
@app.exception_handler(StarletteHTTPException)
async def http_exception_handler(request: Request, exc: StarletteHTTPException):
    """HTTP异常处理"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": True,
            "message": exc.detail,
            "status_code": exc.status_code
        }
    )

@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """请求验证异常处理"""
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={
            "error": True,
            "message": "请求参数验证失败",
            "details": exc.errors()
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理"""
    logger.error(f"未处理的异常: {exc}", exc_info=True)
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "error": True,
            "message": "服务器内部错误" if not settings.DEBUG else str(exc)
        }
    )

# 注册路由
if FULL_IMPORTS:
    # 健康检查路由（优先注册）
    try:
        from app.api import health
        app.include_router(health.router, prefix="/api", tags=["健康检查"])
        logger.info("✓ 健康检查API已注册")
    except ImportError as e:
        logger.warning(f"⚠ 无法导入健康检查API: {e}")

    app.include_router(auth.router, prefix="/api")
    app.include_router(users.router, prefix="/api")
    app.include_router(sync.router, prefix="/api")
    app.include_router(settings_api.router, prefix="/api")
    app.include_router(system.router, prefix="/api")
    app.include_router(profile.router, prefix="/api")
    app.include_router(community.router, prefix="/api/v1/community", tags=["社区管理"])

    # 新增模块路由
    try:
        from app.api.v1 import advertising, question_bank, english_practice, psychology, courses, app_updates
        app.include_router(advertising.router, prefix="/api/v1/advertising", tags=["广告管理"])
        app.include_router(question_bank.router, prefix="/api/v1/question-bank", tags=["题库管理"])
        app.include_router(english_practice.router, prefix="/api/v1/english-practice", tags=["英语练习管理"])
        app.include_router(psychology.router, prefix="/api/v1/psychology", tags=["心理资源库"])
        app.include_router(courses.router, prefix="/api/v1/courses", tags=["课程管理"])
        app.include_router(app_updates.router, prefix="/api/v1/app-updates", tags=["软件更新推送"])
    except ImportError as e:
        print(f"警告: 无法导入新模块路由 ({e})，将在后续步骤中创建")
# 简化模式代码已移除 - 现在只支持完整功能模式

# 根路径
@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "WisCude 后台管理系统 API",
        "version": settings.APP_VERSION,
        "docs": "/docs" if settings.DEBUG else "文档已禁用",
        "status": "running"
    }

# 健康检查
@app.get("/health")
async def health_check():
    """健康检查"""
    # 检查数据库连接
    db_status = db_manager.check_connection()
    
    # 检查Android数据库
    android_db_status = False
    try:
        import os
        android_db_status = os.path.exists(settings.ANDROID_DB_PATH)
    except Exception:
        android_db_status = False
    
    return {
        "status": "healthy" if db_status else "unhealthy",
        "timestamp": time.time(),
        "database": {
            "postgresql": "connected" if db_status else "disconnected",
            "android_sqlite": "available" if android_db_status else "unavailable"
        },
        "version": settings.APP_VERSION
    }

# 系统信息
@app.get("/info")
async def system_info():
    """系统信息"""
    return {
        "app_name": settings.APP_NAME,
        "version": settings.APP_VERSION,
        "debug": settings.DEBUG,
        "database_url": settings.DATABASE_URL.split("@")[-1] if "@" in settings.DATABASE_URL else "配置错误",
        "android_db_path": settings.ANDROID_DB_PATH,
        "cors_origins": settings.CORS_ORIGINS
    }

# 已删除 create_default_admin 函数 - 使用数据库中的admin表信息进行登录

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
