<template>
  <div class="log-management">
    <!-- 日志控制面板 -->
    <el-card class="control-panel">
      <div class="panel-header">
        <h3>日志管理控制台</h3>
        <div class="panel-actions">
          <el-button type="primary" @click="refreshLogs" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新日志
          </el-button>
          <el-button @click="clearAllLogs" :loading="clearing">
            <el-icon><Delete /></el-icon>
            清理日志
          </el-button>
          <el-button @click="downloadLogs" :loading="downloading">
            <el-icon><Download /></el-icon>
            下载日志
          </el-button>
        </div>
      </div>

      <!-- 日志筛选器 -->
      <div class="log-filters">
        <el-form :model="filters" inline>
          <el-form-item label="日志级别">
            <el-select v-model="filters.level" placeholder="选择级别" @change="handleFilterChange">
              <el-option label="全部级别" value="" />
              <el-option label="DEBUG" value="DEBUG" />
              <el-option label="INFO" value="INFO" />
              <el-option label="WARNING" value="WARNING" />
              <el-option label="ERROR" value="ERROR" />
              <el-option label="CRITICAL" value="CRITICAL" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="filters.dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              @change="handleFilterChange"
            />
          </el-form-item>
          
          <el-form-item label="关键词">
            <el-input
              v-model="filters.keyword"
              placeholder="搜索日志内容"
              @input="handleSearchInput"
              clearable
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-form-item>
          
          <el-form-item label="模块">
            <el-select v-model="filters.module" placeholder="选择模块" @change="handleFilterChange">
              <el-option label="全部模块" value="" />
              <el-option label="系统" value="system" />
              <el-option label="用户" value="user" />
              <el-option label="数据库" value="database" />
              <el-option label="API" value="api" />
              <el-option label="认证" value="auth" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 日志统计概览 -->
    <div class="log-statistics">
      <el-row :gutter="24">
        <el-col :xs="24" :sm="12" :lg="6" v-for="(stat, index) in logStats" :key="index">
          <el-card :class="['stat-card', stat.type]">
            <div class="stat-content">
              <div class="stat-icon" :style="{ backgroundColor: stat.color }">
                <el-icon><component :is="stat.icon" /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stat.value }}</div>
                <div class="stat-label">{{ stat.label }}</div>
                <div class="stat-trend" :class="stat.trend >= 0 ? 'positive' : 'negative'">
                  <el-icon><ArrowUp v-if="stat.trend >= 0" /><ArrowDown v-else /></el-icon>
                  {{ Math.abs(stat.trend) }}%
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 日志配置面板 -->
    <el-card class="log-config-panel">
      <template #header>
        <div class="config-header">
          <span>日志配置管理</span>
          <el-button type="primary" size="small" @click="showConfigDialog = true">
            <el-icon><Setting /></el-icon>
            配置设置
          </el-button>
        </div>
      </template>

      <el-row :gutter="24">
        <el-col :span="8">
          <div class="config-item">
            <div class="config-label">当前日志级别</div>
            <div class="config-value">{{ logConfig.logLevel }}</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="config-item">
            <div class="config-label">日志格式</div>
            <div class="config-value">{{ logConfig.logFormat === 'standard' ? '标准格式' : logConfig.logFormat === 'json' ? 'JSON格式' : '详细格式' }}</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="config-item">
            <div class="config-label">保留天数</div>
            <div class="config-value">{{ logConfig.retentionDays }} 天</div>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="24" style="margin-top: 16px;">
        <el-col :span="8">
          <div class="config-item">
            <div class="config-label">文件大小限制</div>
            <div class="config-value">{{ logConfig.maxFileSize }} MB</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="config-item">
            <div class="config-label">日志轮转</div>
            <div class="config-value">
              <el-tag :type="logConfig.enableRotation ? 'success' : 'info'">
                {{ logConfig.enableRotation ? '已启用' : '已禁用' }}
              </el-tag>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="config-item">
            <div class="config-label">启用的日志类型</div>
            <div class="config-value">{{ logConfig.enabledLogTypes.length }} 种</div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 实时日志流 -->
    <el-card class="log-stream">
      <template #header>
        <div class="stream-header">
          <span>实时日志流</span>
          <div class="stream-controls">
            <el-switch
              v-model="realTimeEnabled"
              @change="toggleRealTime"
              active-text="实时更新"
              inactive-text="暂停更新"
            />
            <el-button size="small" @click="clearLogStream">
              <el-icon><Delete /></el-icon>
              清空显示
            </el-button>
          </div>
        </div>
      </template>

      <div class="log-stream-container" ref="logStreamRef">
        <div
          v-for="(log, index) in filteredLogs"
          :key="index"
          :class="['log-entry', `level-${log.level.toLowerCase()}`]"
          @click="showLogDetails(log)"
        >
          <div class="log-timestamp">{{ formatTimestamp(log.timestamp) }}</div>
          <div class="log-level" :class="`level-${log.level.toLowerCase()}`">
            {{ log.level }}
          </div>
          <div class="log-module">{{ log.module }}</div>
          <div class="log-message">{{ log.message }}</div>
          <div class="log-actions">
            <el-button size="small" text @click.stop="copyLogEntry(log)">
              <el-icon><CopyDocument /></el-icon>
            </el-button>
            <el-button size="small" text @click.stop="showLogDetails(log)">
              <el-icon><View /></el-icon>
            </el-button>
          </div>
        </div>
        
        <div v-if="filteredLogs.length === 0" class="empty-logs">
          <el-empty description="暂无日志数据" />
        </div>
      </div>

      <!-- 分页 -->
      <div class="log-pagination">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[20, 50, 100, 200]"
          :total="totalLogs"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 日志详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="日志详情"
      width="70%"
      :before-close="handleDetailClose"
    >
      <div v-if="selectedLog" class="log-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="时间戳">
            {{ formatTimestamp(selectedLog.timestamp) }}
          </el-descriptions-item>
          <el-descriptions-item label="日志级别">
            <el-tag :type="getLevelTagType(selectedLog.level)">
              {{ selectedLog.level }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="模块">
            {{ selectedLog.module }}
          </el-descriptions-item>
          <el-descriptions-item label="用户">
            {{ selectedLog.user || '系统' }}
          </el-descriptions-item>
          <el-descriptions-item label="IP地址">
            {{ selectedLog.ip || 'N/A' }}
          </el-descriptions-item>
          <el-descriptions-item label="请求ID">
            {{ selectedLog.requestId || 'N/A' }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="log-message-detail">
          <h4>详细信息</h4>
          <el-input
            v-model="selectedLog.message"
            type="textarea"
            :rows="6"
            readonly
            class="log-message-textarea"
          />
        </div>
        
        <div v-if="selectedLog.stackTrace" class="log-stack-trace">
          <h4>堆栈跟踪</h4>
          <el-input
            v-model="selectedLog.stackTrace"
            type="textarea"
            :rows="8"
            readonly
            class="stack-trace-textarea"
          />
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="copyLogDetails">复制详情</el-button>
          <el-button type="primary" @click="detailDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 日志配置对话框 -->
    <el-dialog
      v-model="showConfigDialog"
      title="日志配置设置"
      width="800px"
      :before-close="handleConfigClose"
    >
      <el-form :model="logConfig" ref="logConfigFormRef" label-width="120px">
        <el-divider content-position="left">基础配置</el-divider>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="日志级别" prop="logLevel">
              <el-select v-model="logConfig.logLevel" placeholder="请选择日志级别">
                <el-option label="DEBUG" value="DEBUG" />
                <el-option label="INFO" value="INFO" />
                <el-option label="WARNING" value="WARNING" />
                <el-option label="ERROR" value="ERROR" />
                <el-option label="CRITICAL" value="CRITICAL" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="日志格式" prop="logFormat">
              <el-select v-model="logConfig.logFormat" placeholder="请选择日志格式">
                <el-option label="标准格式" value="standard" />
                <el-option label="JSON格式" value="json" />
                <el-option label="详细格式" value="detailed" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="日志文件路径" prop="logFilePath">
              <el-input v-model="logConfig.logFilePath" placeholder="/var/log/wiscude/" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单文件最大大小" prop="maxFileSize">
              <el-input-number v-model="logConfig.maxFileSize" :min="1" :max="1000" />
              <span style="margin-left: 8px;">MB</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="日志保留天数" prop="retentionDays">
              <el-input-number v-model="logConfig.retentionDays" :min="1" :max="365" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="启用日志轮转">
              <el-switch v-model="logConfig.enableRotation" />
              <span style="margin-left: 8px; color: #999;">自动轮转日志文件</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="日志类型配置">
          <el-checkbox-group v-model="logConfig.enabledLogTypes">
            <el-checkbox label="system">系统日志</el-checkbox>
            <el-checkbox label="access">访问日志</el-checkbox>
            <el-checkbox label="error">错误日志</el-checkbox>
            <el-checkbox label="security">安全日志</el-checkbox>
            <el-checkbox label="audit">审计日志</el-checkbox>
            <el-checkbox label="performance">性能日志</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-divider content-position="left">文件管理</el-divider>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="日志类型">
              <el-select v-model="logConfig.viewLogType" placeholder="选择日志类型" @change="loadLogFiles">
                <el-option label="系统日志" value="system" />
                <el-option label="访问日志" value="access" />
                <el-option label="错误日志" value="error" />
                <el-option label="安全日志" value="security" />
                <el-option label="审计日志" value="audit" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="日志文件">
              <el-select v-model="logConfig.selectedLogFile" placeholder="选择日志文件" @change="loadLogContent">
                <el-option
                  v-for="file in logConfig.logFiles"
                  :key="file.name"
                  :label="`${file.name} (${file.size})`"
                  :value="file.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="操作">
              <el-button type="primary" @click="downloadLogFile" :disabled="!logConfig.selectedLogFile" size="small">
                下载日志
              </el-button>
              <el-button type="warning" @click="clearLogFile" :disabled="!logConfig.selectedLogFile" size="small">
                清空日志
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="resetLogConfig">重置</el-button>
          <el-button @click="showConfigDialog = false">取消</el-button>
          <el-button type="primary" @click="saveLogConfig" :loading="configSaving">保存配置</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh, Delete, Download, Search, ArrowUp, ArrowDown,
  CopyDocument, View, Document, Warning, CircleClose, InfoFilled, Setting
} from '@element-plus/icons-vue'

// 接口定义
interface LogEntry {
  id: string
  timestamp: string
  level: string
  module: string
  message: string
  user?: string
  ip?: string
  requestId?: string
  stackTrace?: string
}

// 响应式数据
const loading = ref(false)
const clearing = ref(false)
const downloading = ref(false)
const realTimeEnabled = ref(true)
const detailDialogVisible = ref(false)
const selectedLog = ref<LogEntry | null>(null)
const logStreamRef = ref<HTMLElement>()

// 筛选器
const filters = reactive({
  level: '',
  dateRange: [] as string[],
  keyword: '',
  module: ''
})

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 50
})

const totalLogs = ref(0)

// 日志配置相关
const showConfigDialog = ref(false)
const configSaving = ref(false)
const logConfigFormRef = ref()

const logConfig = reactive({
  logLevel: 'INFO',
  logFormat: 'standard',
  logFilePath: '/var/log/wiscude/',
  maxFileSize: 100,
  retentionDays: 30,
  enableRotation: true,
  enabledLogTypes: ['system', 'access', 'error', 'security'],
  viewLogType: '',
  selectedLogFile: '',
  logFiles: [] as Array<{name: string, size: string}>,
  logContent: ''
})

// 日志统计数据
const logStats = reactive([
  {
    label: '今日日志',
    value: '12,456',
    trend: 8.5,
    color: '#3b82f6',
    icon: 'Document',
    type: 'info'
  },
  {
    label: '错误日志',
    value: '23',
    trend: -12.3,
    color: '#ef4444',
    icon: 'CircleClose',
    type: 'error'
  },
  {
    label: '警告日志',
    value: '156',
    trend: 5.2,
    color: '#f59e0b',
    icon: 'Warning',
    type: 'warning'
  },
  {
    label: '信息日志',
    value: '11,277',
    trend: 15.7,
    color: '#10b981',
    icon: 'InfoFilled',
    type: 'success'
  }
])

// 模拟日志数据
const logs = ref<LogEntry[]>([])

// 生成模拟日志数据
const generateMockLogs = () => {
  const levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
  const modules = ['system', 'user', 'database', 'api', 'auth']
  const messages = [
    '用户登录成功',
    '数据库连接建立',
    'API请求处理完成',
    '缓存更新成功',
    '文件上传完成',
    '权限验证失败',
    '数据库查询超时',
    '内存使用率过高',
    '磁盘空间不足',
    '网络连接异常'
  ]

  const mockLogs: LogEntry[] = []
  for (let i = 0; i < 200; i++) {
    const timestamp = new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000)
    mockLogs.push({
      id: `log-${i}`,
      timestamp: timestamp.toISOString(),
      level: levels[Math.floor(Math.random() * levels.length)],
      module: modules[Math.floor(Math.random() * modules.length)],
      message: messages[Math.floor(Math.random() * messages.length)],
      user: Math.random() > 0.3 ? `user${Math.floor(Math.random() * 100)}` : undefined,
      ip: `192.168.1.${Math.floor(Math.random() * 255)}`,
      requestId: `req-${Math.random().toString(36).substr(2, 9)}`
    })
  }
  
  return mockLogs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
}

// 计算过滤后的日志
const filteredLogs = computed(() => {
  let filtered = logs.value

  if (filters.level) {
    filtered = filtered.filter(log => log.level === filters.level)
  }

  if (filters.module) {
    filtered = filtered.filter(log => log.module === filters.module)
  }

  if (filters.keyword) {
    const keyword = filters.keyword.toLowerCase()
    filtered = filtered.filter(log => 
      log.message.toLowerCase().includes(keyword) ||
      log.module.toLowerCase().includes(keyword) ||
      (log.user && log.user.toLowerCase().includes(keyword))
    )
  }

  if (filters.dateRange && filters.dateRange.length === 2) {
    const [start, end] = filters.dateRange
    filtered = filtered.filter(log => {
      const logTime = new Date(log.timestamp).getTime()
      return logTime >= new Date(start).getTime() && logTime <= new Date(end).getTime()
    })
  }

  totalLogs.value = filtered.length
  
  // 分页
  const start = (pagination.currentPage - 1) * pagination.pageSize
  const end = start + pagination.pageSize
  return filtered.slice(start, end)
})

// 方法实现
const refreshLogs = async () => {
  loading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    logs.value = generateMockLogs()
    ElMessage.success('日志刷新成功')
  } catch (error) {
    ElMessage.error('日志刷新失败')
  } finally {
    loading.value = false
  }
}

const clearAllLogs = async () => {
  try {
    await ElMessageBox.confirm('确定要清理所有日志吗？此操作不可恢复。', '确认清理', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    clearing.value = true
    await new Promise(resolve => setTimeout(resolve, 1500))
    logs.value = []
    clearing.value = false
    ElMessage.success('日志清理成功')
  } catch {
    // 用户取消
  }
}

const downloadLogs = async () => {
  downloading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 2000))
    // 模拟下载日志文件
    const blob = new Blob([JSON.stringify(logs.value, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `system-logs-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    ElMessage.success('日志下载成功')
  } catch (error) {
    ElMessage.error('日志下载失败')
  } finally {
    downloading.value = false
  }
}

const handleFilterChange = () => {
  pagination.currentPage = 1
}

const handleSearchInput = () => {
  // 防抖搜索
  clearTimeout(searchTimer)
  searchTimer = setTimeout(() => {
    handleFilterChange()
  }, 500)
}

let searchTimer: NodeJS.Timeout

const toggleRealTime = (enabled: boolean) => {
  if (enabled) {
    startRealTimeUpdates()
    ElMessage.success('实时更新已开启')
  } else {
    stopRealTimeUpdates()
    ElMessage.info('实时更新已暂停')
  }
}

const clearLogStream = () => {
  logs.value = []
  ElMessage.success('日志显示已清空')
}

const showLogDetails = (log: LogEntry) => {
  selectedLog.value = { ...log }
  detailDialogVisible.value = true
}

const handleDetailClose = () => {
  detailDialogVisible.value = false
  selectedLog.value = null
}

const copyLogEntry = async (log: LogEntry) => {
  try {
    const logText = `[${log.timestamp}] ${log.level} ${log.module}: ${log.message}`
    await navigator.clipboard.writeText(logText)
    ElMessage.success('日志条目已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const copyLogDetails = async () => {
  if (!selectedLog.value) return

  try {
    const details = JSON.stringify(selectedLog.value, null, 2)
    await navigator.clipboard.writeText(details)
    ElMessage.success('日志详情已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
}

const formatTimestamp = (timestamp: string) => {
  return new Date(timestamp).toLocaleString()
}

const getLevelTagType = (level: string) => {
  switch (level) {
    case 'DEBUG':
      return ''
    case 'INFO':
      return 'success'
    case 'WARNING':
      return 'warning'
    case 'ERROR':
    case 'CRITICAL':
      return 'danger'
    default:
      return ''
  }
}

// 实时更新相关
let realTimeTimer: NodeJS.Timeout | null = null

const startRealTimeUpdates = () => {
  if (realTimeTimer) return

  realTimeTimer = setInterval(() => {
    if (realTimeEnabled.value) {
      // 模拟新日志条目
      const newLog = generateMockLogs()[0]
      logs.value.unshift(newLog)

      // 限制日志数量
      if (logs.value.length > 1000) {
        logs.value = logs.value.slice(0, 1000)
      }

      // 自动滚动到顶部
      nextTick(() => {
        if (logStreamRef.value) {
          logStreamRef.value.scrollTop = 0
        }
      })
    }
  }, 3000) // 每3秒添加一条新日志
}

const stopRealTimeUpdates = () => {
  if (realTimeTimer) {
    clearInterval(realTimeTimer)
    realTimeTimer = null
  }
}

// 日志配置相关方法
const handleConfigClose = () => {
  showConfigDialog.value = false
}

const loadLogFiles = (logType: string) => {
  // 模拟加载日志文件列表
  const mockFiles = [
    { name: `${logType}-2024-01-15.log`, size: '2.3MB' },
    { name: `${logType}-2024-01-14.log`, size: '1.8MB' },
    { name: `${logType}-2024-01-13.log`, size: '2.1MB' }
  ]
  logConfig.logFiles = mockFiles
  logConfig.selectedLogFile = ''
  logConfig.logContent = ''
}

const loadLogContent = (fileName: string) => {
  // 模拟加载日志内容
  const mockContent = `[2024-01-15 10:30:15] INFO system: 系统启动完成
[2024-01-15 10:30:16] INFO database: 数据库连接建立
[2024-01-15 10:30:17] INFO api: API服务启动
[2024-01-15 10:30:18] WARNING cache: 缓存预热中
[2024-01-15 10:30:19] INFO system: 所有服务就绪`
  logConfig.logContent = mockContent
}

const downloadLogFile = async () => {
  if (!logConfig.selectedLogFile) return

  try {
    // 模拟下载日志文件
    const blob = new Blob([logConfig.logContent], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = logConfig.selectedLogFile
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    ElMessage.success('日志文件下载成功')
  } catch (error) {
    ElMessage.error('下载失败')
  }
}

const clearLogFile = async () => {
  if (!logConfig.selectedLogFile) return

  try {
    await ElMessageBox.confirm(`确定要清空日志文件 ${logConfig.selectedLogFile} 吗？`, '确认清空', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    logConfig.logContent = ''
    ElMessage.success('日志文件已清空')
  } catch {
    // 用户取消
  }
}

const saveLogConfig = async () => {
  try {
    configSaving.value = true
    // 模拟保存配置
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('日志配置保存成功')
    showConfigDialog.value = false
  } catch (error) {
    ElMessage.error('配置保存失败')
  } finally {
    configSaving.value = false
  }
}

const resetLogConfig = () => {
  logConfig.logLevel = 'INFO'
  logConfig.logFormat = 'standard'
  logConfig.logFilePath = '/var/log/wiscude/'
  logConfig.maxFileSize = 100
  logConfig.retentionDays = 30
  logConfig.enableRotation = true
  logConfig.enabledLogTypes = ['system', 'access', 'error', 'security']
  logConfig.viewLogType = ''
  logConfig.selectedLogFile = ''
  logConfig.logFiles = []
  logConfig.logContent = ''
  ElMessage.success('配置已重置')
}

// 生命周期
onMounted(() => {
  logs.value = generateMockLogs()
  if (realTimeEnabled.value) {
    startRealTimeUpdates()
  }
})

onUnmounted(() => {
  stopRealTimeUpdates()
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
})
</script>

<style scoped>
@import '@/styles/design-system.scss';

.log-management {
  padding: var(--spacing-4);

  /* 控制面板 */
  .control-panel {
    margin-bottom: var(--spacing-6);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow-sm);

    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-4);

      h3 {
        margin: 0;
        font-size: var(--font-size-lg);
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
      }

      .panel-actions {
        display: flex;
        gap: var(--spacing-3);

        .el-button {
          border-radius: var(--radius-lg);
          font-weight: var(--font-weight-medium);
        }
      }
    }

    .log-filters {
      .el-form {
        .el-form-item {
          margin-bottom: var(--spacing-4);
          margin-right: var(--spacing-6);

          .el-form-item__label {
            font-weight: var(--font-weight-medium);
            color: var(--text-primary);
          }

          .el-select,
          .el-date-editor,
          .el-input {
            border-radius: var(--radius-lg);
          }
        }
      }
    }
  }

  /* 日志统计 */
  .log-statistics {
    margin-bottom: var(--spacing-6);

    .stat-card {
      border-radius: var(--radius-xl);
      border: 1px solid var(--border-light);
      box-shadow: var(--shadow-sm);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
      }

      &.info {
        border-left: 4px solid var(--primary-color);
      }

      &.success {
        border-left: 4px solid var(--success-color);
      }

      &.warning {
        border-left: 4px solid var(--warning-color);
      }

      &.error {
        border-left: 4px solid var(--error-color);
      }

      .stat-content {
        display: flex;
        align-items: center;
        gap: var(--spacing-4);
        padding: var(--spacing-5);

        .stat-icon {
          width: 48px;
          height: 48px;
          border-radius: var(--radius-lg);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: var(--font-size-lg);
        }

        .stat-info {
          flex: 1;

          .stat-value {
            font-size: var(--font-size-xl);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            margin-bottom: var(--spacing-1);
          }

          .stat-label {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            margin-bottom: var(--spacing-2);
          }

          .stat-trend {
            display: flex;
            align-items: center;
            gap: var(--spacing-1);
            font-size: var(--font-size-xs);
            font-weight: var(--font-weight-medium);

            &.positive {
              color: var(--success-color);
            }

            &.negative {
              color: var(--error-color);
            }
          }
        }
      }
    }
  }

  /* 日志流 */
  .log-stream {
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow-sm);

    .el-card__header {
      border-bottom: 1px solid var(--border-light);

      .stream-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .stream-controls {
          display: flex;
          align-items: center;
          gap: var(--spacing-3);
        }
      }
    }

    .log-stream-container {
      max-height: 600px;
      overflow-y: auto;
      border-radius: var(--radius-lg);
      background: var(--bg-light);

      .log-entry {
        display: grid;
        grid-template-columns: 180px 80px 100px 1fr 80px;
        gap: var(--spacing-3);
        padding: var(--spacing-3) var(--spacing-4);
        border-bottom: 1px solid var(--border-light);
        transition: all 0.2s ease;
        cursor: pointer;

        &:hover {
          background: var(--bg-secondary);
        }

        &:last-child {
          border-bottom: none;
        }

        .log-timestamp {
          font-size: var(--font-size-xs);
          color: var(--text-secondary);
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }

        .log-level {
          font-size: var(--font-size-xs);
          font-weight: var(--font-weight-bold);
          padding: 2px 6px;
          border-radius: var(--radius-sm);
          text-align: center;

          &.level-debug {
            background: var(--bg-secondary);
            color: var(--text-secondary);
          }

          &.level-info {
            background: var(--success-light);
            color: var(--success-color);
          }

          &.level-warning {
            background: var(--warning-light);
            color: var(--warning-color);
          }

          &.level-error,
          &.level-critical {
            background: var(--error-light);
            color: var(--error-color);
          }
        }

        .log-module {
          font-size: var(--font-size-xs);
          color: var(--text-tertiary);
          font-weight: var(--font-weight-medium);
        }

        .log-message {
          font-size: var(--font-size-sm);
          color: var(--text-primary);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .log-actions {
          display: flex;
          gap: var(--spacing-1);
          opacity: 0;
          transition: opacity 0.2s ease;

          .el-button {
            padding: 4px;
          }
        }

        &:hover .log-actions {
          opacity: 1;
        }
      }

      .empty-logs {
        padding: var(--spacing-8);
        text-align: center;
      }
    }

    .log-pagination {
      padding: var(--spacing-4);
      border-top: 1px solid var(--border-light);
      display: flex;
      justify-content: center;
    }
  }

  /* 日志详情对话框 */
  .log-detail {
    .log-message-detail,
    .log-stack-trace {
      margin-top: var(--spacing-4);

      h4 {
        margin: 0 0 var(--spacing-3) 0;
        font-size: var(--font-size-base);
        color: var(--text-primary);
      }

      .log-message-textarea,
      .stack-trace-textarea {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: var(--font-size-sm);
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-3);
  }
}

/* 响应式设计 */
@include respond-to('lg') {
  .log-management {
    .control-panel {
      .panel-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-3);
      }

      .log-filters .el-form .el-form-item {
        margin-right: 0;
        margin-bottom: var(--spacing-4);
      }
    }

    .log-statistics .el-col {
      margin-bottom: var(--spacing-4);
    }
  }
}

@include respond-to('md') {
  .log-management {
    padding: var(--spacing-3);

    .log-stream .log-stream-container .log-entry {
      grid-template-columns: 1fr;
      gap: var(--spacing-2);

      .log-timestamp,
      .log-level,
      .log-module {
        font-size: var(--font-size-xs);
      }

      .log-actions {
        opacity: 1;
        justify-content: flex-end;
      }
    }
  }
}

@include respond-to('sm') {
  .log-management {
    .control-panel {
      .log-filters .el-form {
        .el-form-item {
          width: 100%;

          .el-select,
          .el-date-editor,
          .el-input {
            width: 100%;
          }
        }
      }
    }
  }
}

/* 日志配置面板样式 */
.log-config-panel {
  margin-bottom: 24px;

  .config-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .config-item {
    .config-label {
      font-size: 12px;
      color: var(--text-secondary);
      margin-bottom: 4px;
    }

    .config-value {
      font-size: 14px;
      font-weight: 500;
      color: var(--text-primary);
    }
  }
}
</style>
