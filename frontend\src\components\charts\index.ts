/**
 * 图表组件导出文件
 * 统一导出所有图表组件和相关工具
 */

import BaseChart from './BaseChart.vue'
import LineChart from './LineChart.vue'
import PieChart from './PieChart.vue'
import BarChart from './BarChart.vue'
import RadarChart from './RadarChart.vue'
import Doughnut<PERSON>hart from './DoughnutChart.vue'

// 导出组件
export {
  BaseChart,
  LineChart,
  PieChart,
  BarChart,
  RadarChart,
  DoughnutChart
}

// 图表主题配置
export const chartThemes = {
  default: 'default',
  dark: 'dark',
  vintage: 'vintage',
  macarons: 'macarons',
  infographic: 'infographic',
  shine: 'shine',
  roma: 'roma'
}

// 常用颜色配置
export const chartColors = {
  primary: ['#409eff', '#67c23a', '#e6a23c', '#f56c6c', '#909399'],
  business: ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1'],
  gradient: ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe'],
  pastel: ['#a8e6cf', '#dcedc1', '#ffd3a5', '#fd9853', '#ff8a80'],
  vibrant: ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7']
}

// 图表工具函数
export const chartUtils = {
  /**
   * 格式化数值
   */
  formatValue: (value: number, type: 'number' | 'percentage' | 'currency' = 'number'): string => {
    switch (type) {
      case 'percentage':
        return `${(value * 100).toFixed(1)}%`
      case 'currency':
        return `¥${value.toLocaleString()}`
      default:
        return value.toLocaleString()
    }
  },

  /**
   * 生成渐变色
   */
  generateGradient: (color1: string, color2: string, direction: 'horizontal' | 'vertical' = 'vertical') => {
    return {
      type: 'linear',
      x: 0,
      y: direction === 'vertical' ? 0 : 1,
      x2: direction === 'vertical' ? 0 : 1,
      y2: direction === 'vertical' ? 1 : 0,
      colorStops: [
        { offset: 0, color: color1 },
        { offset: 1, color: color2 }
      ]
    }
  },

  /**
   * 计算合适的图表尺寸
   */
  calculateSize: (container: HTMLElement, aspectRatio: number = 16/9) => {
    const containerWidth = container.clientWidth
    const containerHeight = container.clientHeight
    
    let width = containerWidth
    let height = containerWidth / aspectRatio
    
    if (height > containerHeight) {
      height = containerHeight
      width = containerHeight * aspectRatio
    }
    
    return { width, height }
  },

  /**
   * 数据预处理
   */
  preprocessData: (data: any[], options: {
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
    limit?: number
    groupBy?: string
  } = {}) => {
    let result = [...data]
    
    // 排序
    if (options.sortBy) {
      result.sort((a, b) => {
        const aVal = a[options.sortBy!]
        const bVal = b[options.sortBy!]
        const order = options.sortOrder === 'desc' ? -1 : 1
        
        if (typeof aVal === 'number' && typeof bVal === 'number') {
          return (aVal - bVal) * order
        }
        return String(aVal).localeCompare(String(bVal)) * order
      })
    }
    
    // 限制数量
    if (options.limit) {
      result = result.slice(0, options.limit)
    }
    
    // 分组
    if (options.groupBy) {
      const grouped = result.reduce((acc, item) => {
        const key = item[options.groupBy!]
        if (!acc[key]) acc[key] = []
        acc[key].push(item)
        return acc
      }, {} as Record<string, any[]>)
      
      return Object.entries(grouped).map(([key, items]) => ({
        name: key,
        data: items
      }))
    }
    
    return result
  },

  /**
   * 生成时间序列数据
   */
  generateTimeSeriesData: (
    startDate: Date,
    endDate: Date,
    interval: 'day' | 'week' | 'month' = 'day'
  ): string[] => {
    const dates: string[] = []
    const current = new Date(startDate)
    
    while (current <= endDate) {
      dates.push(current.toISOString().split('T')[0])
      
      switch (interval) {
        case 'day':
          current.setDate(current.getDate() + 1)
          break
        case 'week':
          current.setDate(current.getDate() + 7)
          break
        case 'month':
          current.setMonth(current.getMonth() + 1)
          break
      }
    }
    
    return dates
  },

  /**
   * 计算统计信息
   */
  calculateStats: (data: number[]) => {
    if (data.length === 0) return null
    
    const sorted = [...data].sort((a, b) => a - b)
    const sum = data.reduce((acc, val) => acc + val, 0)
    const mean = sum / data.length
    
    return {
      min: sorted[0],
      max: sorted[sorted.length - 1],
      sum,
      mean,
      median: sorted.length % 2 === 0
        ? (sorted[sorted.length / 2 - 1] + sorted[sorted.length / 2]) / 2
        : sorted[Math.floor(sorted.length / 2)],
      count: data.length
    }
  }
}

// 图表配置预设
export const chartPresets = {
  /**
   * 线图预设
   */
  line: {
    trend: {
      smooth: true,
      area: true,
      showDataZoom: true,
      colors: chartColors.primary
    },
    comparison: {
      smooth: false,
      area: false,
      showLegend: true,
      colors: chartColors.business
    }
  },

  /**
   * 饼图预设
   */
  pie: {
    donut: {
      innerRadius: '40%',
      outerRadius: '70%',
      showLabelLine: false,
      colors: chartColors.pastel
    },
    rose: {
      roseType: 'radius',
      innerRadius: '20%',
      colors: chartColors.gradient
    }
  },

  /**
   * 柱图预设
   */
  bar: {
    comparison: {
      horizontal: false,
      stack: false,
      showDataZoom: true,
      colors: chartColors.vibrant
    },
    stacked: {
      horizontal: false,
      stack: true,
      colors: chartColors.business
    },
    horizontal: {
      horizontal: true,
      stack: false,
      colors: chartColors.primary
    }
  }
}

// 响应式图表Hook
export const useResponsiveChart = () => {
  const getResponsiveSize = (containerWidth: number) => {
    if (containerWidth < 576) {
      return { width: '100%', height: '250px' }
    } else if (containerWidth < 768) {
      return { width: '100%', height: '300px' }
    } else if (containerWidth < 992) {
      return { width: '100%', height: '350px' }
    } else {
      return { width: '100%', height: '400px' }
    }
  }

  const getResponsiveOptions = (containerWidth: number) => {
    const isMobile = containerWidth < 768
    
    return {
      showLegend: !isMobile,
      showDataZoom: !isMobile,
      grid: {
        left: isMobile ? '5%' : '3%',
        right: isMobile ? '5%' : '4%',
        top: isMobile ? '15%' : '10%',
        bottom: isMobile ? '15%' : '10%'
      },
      legend: {
        orient: isMobile ? 'horizontal' : 'vertical',
        top: isMobile ? 'top' : 'center',
        right: isMobile ? 'center' : 10
      }
    }
  }

  return {
    getResponsiveSize,
    getResponsiveOptions
  }
}

// 默认导出
export default {
  BaseChart,
  LineChart,
  PieChart,
  BarChart,
  RadarChart,
  DoughnutChart,
  chartThemes,
  chartColors,
  chartUtils,
  chartPresets,
  useResponsiveChart
}
