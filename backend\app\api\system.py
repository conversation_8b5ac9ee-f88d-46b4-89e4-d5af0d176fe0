"""
系统信息相关API端点
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
import psutil
import os
import time
from datetime import datetime, timedelta
import logging

from app.core.database import get_db
from app.core.security import get_current_user, get_current_active_superuser

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/system", tags=["系统信息"])

@router.get("/info", summary="获取系统信息")
async def get_system_info(
    current_user: dict = Depends(get_current_user)
):
    """获取系统运行信息"""
    try:
        # 获取系统启动时间
        boot_time = psutil.boot_time()
        uptime_seconds = time.time() - boot_time
        
        # 获取内存使用情况
        memory = psutil.virtual_memory()
        
        # 获取CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # 获取磁盘使用情况
        disk = psutil.disk_usage('/')
        
        # 获取进程信息
        process_count = len(psutil.pids())
        
        # 获取网络连接数
        try:
            connections = len(psutil.net_connections())
        except:
            connections = 0
        
        system_info = {
            "version": "1.0.0",
            "uptime": int(uptime_seconds),
            "uptime_formatted": str(timedelta(seconds=int(uptime_seconds))),
            "memory_usage": {
                "total": memory.total,
                "available": memory.available,
                "used": memory.used,
                "percent": memory.percent
            },
            "cpu_usage": cpu_percent,
            "disk_usage": {
                "total": disk.total,
                "used": disk.used,
                "free": disk.free,
                "percent": (disk.used / disk.total) * 100
            },
            "process_count": process_count,
            "network_connections": connections,
            "database_status": "connected",  # 简化处理
            "cache_status": "active",        # 简化处理
            "timestamp": datetime.utcnow().isoformat()
        }
        
        return system_info
    except Exception as e:
        logger.error(f"获取系统信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取系统信息失败"
        )

@router.get("/health", summary="系统健康检查")
async def health_check():
    """系统健康检查"""
    try:
        # 检查磁盘空间
        disk = psutil.disk_usage('/')
        disk_usage_percent = (disk.used / disk.total) * 100
        
        # 检查内存使用
        memory = psutil.virtual_memory()
        memory_usage_percent = memory.percent
        
        # 检查CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        
        health_status = {
            "status": "healthy",
            "checks": {
                "disk_space": {
                    "status": "ok" if disk_usage_percent < 90 else "warning",
                    "usage_percent": disk_usage_percent,
                    "message": "磁盘空间正常" if disk_usage_percent < 90 else "磁盘空间不足"
                },
                "memory": {
                    "status": "ok" if memory_usage_percent < 85 else "warning",
                    "usage_percent": memory_usage_percent,
                    "message": "内存使用正常" if memory_usage_percent < 85 else "内存使用率过高"
                },
                "cpu": {
                    "status": "ok" if cpu_percent < 80 else "warning",
                    "usage_percent": cpu_percent,
                    "message": "CPU使用正常" if cpu_percent < 80 else "CPU使用率过高"
                }
            },
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # 如果有任何警告，整体状态设为警告
        if any(check["status"] == "warning" for check in health_status["checks"].values()):
            health_status["status"] = "warning"
        
        return health_status
    except Exception as e:
        logger.error(f"系统健康检查失败: {e}")
        return {
            "status": "error",
            "message": f"健康检查失败: {str(e)}",
            "timestamp": datetime.utcnow().isoformat()
        }

@router.post("/restart", summary="重启系统服务")
async def restart_system(
    current_user: dict = Depends(get_current_active_superuser)
):
    """重启系统服务（仅限超级管理员）"""
    try:
        logger.warning(f"用户 {current_user.get('username')} 请求重启系统")
        
        # 这里应该实现实际的重启逻辑
        # 由于安全考虑，这里只是返回一个消息
        return {
            "success": True,
            "message": "系统重启请求已接收，请手动重启服务"
        }
    except Exception as e:
        logger.error(f"系统重启失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="系统重启失败"
        )

@router.get("/processes", summary="获取进程信息")
async def get_processes(
    current_user: dict = Depends(get_current_active_superuser)
):
    """获取系统进程信息"""
    try:
        processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent', 'status']):
            try:
                proc_info = proc.info
                if proc_info['name'] and 'python' in proc_info['name'].lower():
                    processes.append({
                        "pid": proc_info['pid'],
                        "name": proc_info['name'],
                        "cpu_percent": proc_info['cpu_percent'] or 0,
                        "memory_percent": proc_info['memory_percent'] or 0,
                        "status": proc_info['status']
                    })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        # 按CPU使用率排序
        processes.sort(key=lambda x: x['cpu_percent'], reverse=True)
        
        return {
            "processes": processes[:20],  # 只返回前20个进程
            "total_count": len(processes)
        }
    except Exception as e:
        logger.error(f"获取进程信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取进程信息失败"
        )

@router.get("/logs/stats", summary="获取日志统计")
async def get_log_stats(
    current_user: dict = Depends(get_current_user)
):
    """获取日志统计信息"""
    try:
        import glob
        from collections import defaultdict
        
        log_dir = "logs"
        if not os.path.exists(log_dir):
            return {"total_files": 0, "total_size": 0, "level_counts": {}}
        
        total_files = 0
        total_size = 0
        level_counts = defaultdict(int)
        
        # 统计日志文件
        for file_path in glob.glob(os.path.join(log_dir, "*.log*")):
            if os.path.isfile(file_path):
                total_files += 1
                total_size += os.path.getsize(file_path)
                
                # 简单统计日志级别（这里只是示例）
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        for line in f:
                            if 'ERROR' in line:
                                level_counts['ERROR'] += 1
                            elif 'WARNING' in line:
                                level_counts['WARNING'] += 1
                            elif 'INFO' in line:
                                level_counts['INFO'] += 1
                            elif 'DEBUG' in line:
                                level_counts['DEBUG'] += 1
                except:
                    continue
        
        return {
            "total_files": total_files,
            "total_size": total_size,
            "total_size_mb": round(total_size / (1024 * 1024), 2),
            "level_counts": dict(level_counts)
        }
    except Exception as e:
        logger.error(f"获取日志统计失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取日志统计失败"
        )

@router.get("/performance", summary="获取性能指标")
async def get_performance_metrics(
    current_user: dict = Depends(get_current_user)
):
    """获取系统性能指标"""
    try:
        # 获取CPU信息
        cpu_count = psutil.cpu_count()
        cpu_freq = psutil.cpu_freq()
        cpu_times = psutil.cpu_times()
        
        # 获取内存信息
        memory = psutil.virtual_memory()
        swap = psutil.swap_memory()
        
        # 获取磁盘IO
        disk_io = psutil.disk_io_counters()
        
        # 获取网络IO
        net_io = psutil.net_io_counters()
        
        performance_data = {
            "cpu": {
                "count": cpu_count,
                "frequency": {
                    "current": cpu_freq.current if cpu_freq else 0,
                    "min": cpu_freq.min if cpu_freq else 0,
                    "max": cpu_freq.max if cpu_freq else 0
                },
                "times": {
                    "user": cpu_times.user,
                    "system": cpu_times.system,
                    "idle": cpu_times.idle
                }
            },
            "memory": {
                "virtual": {
                    "total": memory.total,
                    "available": memory.available,
                    "used": memory.used,
                    "percent": memory.percent
                },
                "swap": {
                    "total": swap.total,
                    "used": swap.used,
                    "free": swap.free,
                    "percent": swap.percent
                }
            },
            "disk_io": {
                "read_count": disk_io.read_count if disk_io else 0,
                "write_count": disk_io.write_count if disk_io else 0,
                "read_bytes": disk_io.read_bytes if disk_io else 0,
                "write_bytes": disk_io.write_bytes if disk_io else 0
            },
            "network_io": {
                "bytes_sent": net_io.bytes_sent if net_io else 0,
                "bytes_recv": net_io.bytes_recv if net_io else 0,
                "packets_sent": net_io.packets_sent if net_io else 0,
                "packets_recv": net_io.packets_recv if net_io else 0
            },
            "timestamp": datetime.utcnow().isoformat()
        }
        
        return performance_data
    except Exception as e:
        logger.error(f"获取性能指标失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取性能指标失败"
        )
