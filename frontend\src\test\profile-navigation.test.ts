/**
 * 个人资料导航功能测试
 */
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import Layout from '@/components/Layout.vue'
import { useAuthStore } from '@/store/auth'

// Mock Element Plus
vi.mock('element-plus', () => ({
  ElMessage: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  },
  ElMessageBox: {
    confirm: vi.fn().mockResolvedValue('confirm'),
    alert: vi.fn().mockResolvedValue('confirm')
  }
}))

// Mock router
const mockRouter = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: 'Layout',
      component: Layout,
      children: [
        {
          path: 'profile',
          name: 'Profile',
          component: { template: '<div>Profile Page</div>' },
          meta: {
            title: '个人资料',
            requiresAuth: true,
            hidden: true
          }
        },
        {
          path: 'settings',
          name: 'Settings',
          component: { template: '<div>Settings Page</div>' },
          meta: {
            title: '系统设置',
            requiresAuth: true,
            requiresSuperuser: true
          }
        }
      ]
    }
  ]
})

const mockUser = {
  id: 1,
  username: 'testuser',
  full_name: '测试用户',
  email: '<EMAIL>',
  role: 'admin',
  is_superuser: true,
  avatar_url: '/api/profile/avatar/test.jpg'
}

describe('Profile Navigation', () => {
  let wrapper: any
  let authStore: any

  beforeEach(() => {
    setActivePinia(createPinia())
    authStore = useAuthStore()
    
    // Mock auth store
    authStore.user = mockUser
    authStore.isAuthenticated = true
    authStore.logout = vi.fn()

    wrapper = mount(Layout, {
      global: {
        plugins: [mockRouter],
        stubs: {
          'el-container': true,
          'el-aside': true,
          'el-header': true,
          'el-main': true,
          'el-menu': true,
          'el-menu-item': true,
          'el-button': true,
          'el-icon': true,
          'el-breadcrumb': true,
          'el-breadcrumb-item': true,
          'el-dropdown': {
            template: `
              <div class="el-dropdown">
                <div class="user-info" @click="$emit('command', 'profile')">
                  <div class="avatar">{{ user?.username }}</div>
                  <span class="username">{{ user?.full_name }}</span>
                </div>
                <slot name="dropdown"></slot>
              </div>
            `,
            props: ['user'],
            emits: ['command']
          },
          'el-dropdown-menu': true,
          'el-dropdown-item': {
            template: '<div class="dropdown-item" @click="$emit(\'click\')"><slot></slot></div>',
            props: ['command'],
            emits: ['click']
          },
          'el-avatar': {
            template: '<div class="avatar"><slot></slot></div>',
            props: ['size', 'src']
          },
          'router-view': true
        }
      }
    })
  })

  it('应该显示用户信息在顶部导航栏', () => {
    expect(wrapper.find('.user-info').exists()).toBe(true)
    expect(wrapper.text()).toContain('测试用户')
  })

  it('应该包含个人资料菜单项', () => {
    expect(wrapper.text()).toContain('个人资料')
  })

  it('应该包含系统设置菜单项', () => {
    expect(wrapper.text()).toContain('系统设置')
  })

  it('应该包含退出登录菜单项', () => {
    expect(wrapper.text()).toContain('退出登录')
  })

  it('点击个人资料应该导航到profile页面', async () => {
    const routerPushSpy = vi.spyOn(mockRouter, 'push')
    
    // 模拟点击个人资料菜单项
    const component = wrapper.vm
    await component.handleCommand('profile')
    
    expect(routerPushSpy).toHaveBeenCalledWith('/profile')
  })

  it('点击系统设置应该导航到settings页面', async () => {
    const routerPushSpy = vi.spyOn(mockRouter, 'push')
    
    // 模拟点击系统设置菜单项
    const component = wrapper.vm
    await component.handleCommand('settings')
    
    expect(routerPushSpy).toHaveBeenCalledWith('/settings')
  })

  it('个人资料路由应该在侧边栏中隐藏', () => {
    const component = wrapper.vm
    const menuRoutes = component.menuRoutes
    
    // 个人资料路由应该被过滤掉（因为hidden: true）
    const profileRoute = menuRoutes.find((route: any) => route.name === 'Profile')
    expect(profileRoute).toBeUndefined()
  })

  it('系统设置路由应该在侧边栏中显示（对超级用户）', () => {
    const component = wrapper.vm
    const menuRoutes = component.menuRoutes
    
    // 系统设置路由应该显示（用户是超级用户）
    const settingsRoute = menuRoutes.find((route: any) => route.name === 'Settings')
    expect(settingsRoute).toBeDefined()
  })

  it('应该正确显示用户头像', () => {
    expect(wrapper.find('.avatar').exists()).toBe(true)
  })

  it('应该正确显示用户名称', () => {
    expect(wrapper.text()).toContain('测试用户')
  })
})

describe('Profile Route Configuration', () => {
  it('个人资料路由应该正确配置', () => {
    const routes = mockRouter.getRoutes()
    const layoutRoute = routes.find(route => route.name === 'Layout')
    const profileRoute = layoutRoute?.children?.find(child => child.name === 'Profile')
    
    expect(profileRoute).toBeDefined()
    expect(profileRoute?.path).toBe('profile')
    expect(profileRoute?.meta?.title).toBe('个人资料')
    expect(profileRoute?.meta?.hidden).toBe(true)
    expect(profileRoute?.meta?.requiresAuth).toBe(true)
  })

  it('个人资料路由应该要求身份验证', () => {
    const routes = mockRouter.getRoutes()
    const layoutRoute = routes.find(route => route.name === 'Layout')
    const profileRoute = layoutRoute?.children?.find(child => child.name === 'Profile')
    
    expect(profileRoute?.meta?.requiresAuth).toBe(true)
  })

  it('个人资料路由应该在侧边栏中隐藏', () => {
    const routes = mockRouter.getRoutes()
    const layoutRoute = routes.find(route => route.name === 'Layout')
    const profileRoute = layoutRoute?.children?.find(child => child.name === 'Profile')
    
    expect(profileRoute?.meta?.hidden).toBe(true)
  })
})

describe('User Menu Functionality', () => {
  let wrapper: any

  beforeEach(() => {
    setActivePinia(createPinia())
    const authStore = useAuthStore()
    authStore.user = mockUser
    authStore.isAuthenticated = true

    wrapper = mount(Layout, {
      global: {
        plugins: [mockRouter],
        stubs: {
          'el-container': true,
          'el-aside': true,
          'el-header': true,
          'el-main': true,
          'el-menu': true,
          'el-menu-item': true,
          'el-button': true,
          'el-icon': true,
          'el-breadcrumb': true,
          'el-breadcrumb-item': true,
          'el-dropdown': true,
          'el-dropdown-menu': true,
          'el-dropdown-item': true,
          'el-avatar': true,
          'router-view': true
        }
      }
    })
  })

  it('handleCommand方法应该正确处理profile命令', async () => {
    const routerPushSpy = vi.spyOn(mockRouter, 'push')
    const component = wrapper.vm
    
    await component.handleCommand('profile')
    expect(routerPushSpy).toHaveBeenCalledWith('/profile')
  })

  it('handleCommand方法应该正确处理settings命令', async () => {
    const routerPushSpy = vi.spyOn(mockRouter, 'push')
    const component = wrapper.vm
    
    await component.handleCommand('settings')
    expect(routerPushSpy).toHaveBeenCalledWith('/settings')
  })

  it('handleCommand方法应该正确处理logout命令', async () => {
    const authStore = useAuthStore()
    const logoutSpy = vi.spyOn(authStore, 'logout')
    const routerPushSpy = vi.spyOn(mockRouter, 'push')
    const component = wrapper.vm
    
    await component.handleCommand('logout')
    expect(logoutSpy).toHaveBeenCalled()
    expect(routerPushSpy).toHaveBeenCalledWith('/login')
  })
})
