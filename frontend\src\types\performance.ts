/**
 * 性能监控相关的类型定义
 */

// 性能指标数据模型
export interface PerformanceMetrics {
  cpu: {
    usage: number        // CPU使用率 (%)
    cores: number        // CPU核心数
    frequency: number    // CPU频率 (GHz)
    temperature?: number // CPU温度 (°C)
    change?: number      // 变化百分比
  }
  memory: {
    total: number        // 总内存 (GB)
    used: number         // 已用内存 (GB)
    available: number    // 可用内存 (GB)
    usage: number        // 使用率 (%)
    change?: number      // 变化百分比
  }
  disk: {
    total: number        // 总磁盘空间 (GB)
    used: number         // 已用空间 (GB)
    free: number         // 可用空间 (GB)
    usage: number        // 使用率 (%)
    ioRead: number       // 读取速度 (MB/s)
    ioWrite: number      // 写入速度 (MB/s)
    change?: number      // 变化百分比
  }
  network: {
    latency: number      // 网络延迟 (ms)
    uploadSpeed: number  // 上传速度 (MB/s)
    downloadSpeed: number // 下载速度 (MB/s)
    packetsLost: number  // 丢包率 (%)
    change?: number      // 变化百分比
  }
  timestamp: string      // 数据时间戳
}

// 系统健康状态模型
export interface SystemHealth {
  overall: 'healthy' | 'warning' | 'error'
  components: Array<{
    name: string
    status: 'healthy' | 'warning' | 'error'
    statusText: string
    details: string
    lastCheck: string
    color: string
    icon: string
  }>
  uptime: number         // 系统运行时间 (秒)
  version: string        // 系统版本
  lastCheck: Date
}

// 前端性能数据模型
export interface FrontendMetrics {
  pageLoad: {
    domContentLoaded: number  // DOM加载时间 (ms)
    loadComplete: number      // 页面完全加载时间 (ms)
    firstPaint: number        // 首次绘制时间 (ms)
    firstContentfulPaint: number // 首次内容绘制时间 (ms)
  }
  memory: {
    usedJSHeapSize: number    // JS堆已用内存 (MB)
    totalJSHeapSize: number   // JS堆总内存 (MB)
    jsHeapSizeLimit: number   // JS堆内存限制 (MB)
  }
  cache: {
    hitRate: number           // 缓存命中率 (%)
    totalItems: number        // 缓存项总数
    activeItems: number       // 活跃缓存项数
    expiredItems: number      // 过期缓存项数
  }
  api: {
    averageResponseTime: number // 平均API响应时间 (ms)
    requestsPerMinute: number   // 每分钟请求数
    errorRate: number           // 错误率 (%)
    recentRequests: Array<{
      id: string
      method: string
      url: string
      duration: number
      status: number
      timestamp: number
    }>
  }
}

// 历史性能数据
export interface HistoricalData {
  timeRange: string
  dataPoints: Array<{
    timestamp: string
    cpu: number
    memory: number
    disk: number
    network: number
  }>
}

// 性能告警
export interface PerformanceAlert {
  id: string
  title: string
  description: string
  type: 'info' | 'warning' | 'error'
  timestamp: Date
  metric: string
  threshold?: number
  currentValue?: number
  resolved?: boolean
}

// 性能配置
export interface PerformanceConfig {
  enableMonitoring: boolean
  refreshInterval: number
  monitoringMetrics: string[]
  alertThresholds: {
    cpu: number
    memory: number
    disk: number
    responseTime: number
  }
  alertMethods: string[]
  dataRetentionDays: number
  samplingInterval: number
}

// 快速统计数据
export interface QuickStats {
  onlineUsers: number
  apiRequestsPerMinute: number
  errorRate: number
  systemLoad: number
  activeConnections: number
  cacheHitRate: number
}

// 性能指标卡片数据
export interface MetricCard {
  label: string
  value: number | string
  unit: string
  change: number
  color: string
  icon: string
  status: 'normal' | 'warning' | 'error' | 'excellent'
  trend?: 'up' | 'down' | 'stable'
}

// 图表数据
export interface ChartData {
  labels: string[]
  datasets: Array<{
    label: string
    data: number[]
    borderColor: string
    backgroundColor: string
    tension?: number
    fill?: boolean
  }>
}

// 图表配置
export interface ChartOptions {
  responsive: boolean
  maintainAspectRatio: boolean
  plugins: {
    legend: {
      position: 'top' | 'bottom' | 'left' | 'right'
    }
    title?: {
      display: boolean
      text: string
    }
  }
  scales: {
    x?: any
    y?: any
  }
}

// 系统负载数据
export interface SystemLoad {
  label: string
  value: number
  status: 'normal' | 'warning' | 'error'
}

// 网络连接信息
export interface NetworkConnection {
  id: string
  type: string
  localAddress: string
  remoteAddress: string
  status: string
  protocol: string
}

// 进程信息
export interface ProcessInfo {
  pid: number
  name: string
  cpuPercent: number
  memoryPercent: number
  status: string
  startTime: string
}

// 数据服务配置
export interface ServiceConfig {
  baseURL: string
  timeout: number
  retryAttempts: number
  cacheTimeout: number
  enableRealTime: boolean
  websocketURL?: string
}

// API响应格式
export interface ApiResponse<T> {
  success: boolean
  data: T
  message?: string
  timestamp: string
}

// 错误信息
export interface ServiceError {
  code: string
  message: string
  details?: any
  timestamp: Date
}

// 数据更新回调
export type DataUpdateCallback<T> = (data: T) => void

// 错误处理回调
export type ErrorCallback = (error: ServiceError) => void

// 订阅配置
export interface SubscriptionConfig {
  interval?: number
  immediate?: boolean
  onError?: ErrorCallback
}

// 缓存配置
export interface CacheConfig {
  ttl: number          // 缓存生存时间 (秒)
  maxSize: number      // 最大缓存项数
  enablePersist: boolean // 是否持久化到localStorage
}

// 导出的主要接口
export interface PerformanceDataService {
  // 获取实时性能指标
  getRealTimeMetrics(): Promise<PerformanceMetrics>
  
  // 获取历史性能数据
  getHistoricalData(timeRange: string): Promise<HistoricalData>
  
  // 订阅实时更新
  subscribeToUpdates(callback: DataUpdateCallback<PerformanceMetrics>, config?: SubscriptionConfig): () => void
  
  // 获取系统健康状态
  getSystemHealth(): Promise<SystemHealth>
  
  // 获取前端性能数据
  getFrontendMetrics(): FrontendMetrics
  
  // 获取快速统计
  getQuickStats(): Promise<QuickStats>
  
  // 获取性能告警
  getAlerts(): Promise<PerformanceAlert[]>
  
  // 获取性能配置
  getConfig(): Promise<PerformanceConfig>
  
  // 更新性能配置
  updateConfig(config: Partial<PerformanceConfig>): Promise<void>
  
  // 清除缓存
  clearCache(): void
  
  // 销毁服务
  destroy(): void
}
